{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_acdacdd7._.js", "server/edge/chunks/[root-of-the-server]__586f2561._.js", "server/edge/chunks/edge-wrapper_1b2467bc.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!api|_next/static|_next/image|favicon.ico|robots.txt|sitemap.xml|uploads).*){(\\\\.json)}?", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|robots.txt|sitemap.xml|uploads).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "N7q2b64Zt1JT/i+kp0zKQtSwAeXEad1q7osWZzH2+Lo=", "__NEXT_PREVIEW_MODE_ID": "23ecb0f032ddd562a5292314a8e0edd3", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "07b29c8017a3b364d94f115f40662a1dd3bd0674adb615b4e7d16a668ddc5d64", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "277cba5b9b772fd32fff08791be7ffc56f6a1f095108ca77797735b2c886ef14"}}}, "instrumentation": null, "functions": {}}