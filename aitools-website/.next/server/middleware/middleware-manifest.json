{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_acdacdd7._.js", "server/edge/chunks/[root-of-the-server]__586f2561._.js", "server/edge/chunks/edge-wrapper_1b2467bc.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!api|_next/static|_next/image|favicon.ico|robots.txt|sitemap.xml|uploads).*){(\\\\.json)}?", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|robots.txt|sitemap.xml|uploads).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "N7q2b64Zt1JT/i+kp0zKQtSwAeXEad1q7osWZzH2+Lo=", "__NEXT_PREVIEW_MODE_ID": "c07b71071c71b33068609dd5627d4121", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "2894a0855f2081f9e11b5a691c37cc45e35963087ca7cfc1c9a3d54d540afb99", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "37dc538dbc2c65b774d76fbe592a7d63b98cbabefc7c48e79059e6c85d5c8f34"}}}, "instrumentation": null, "functions": {}}