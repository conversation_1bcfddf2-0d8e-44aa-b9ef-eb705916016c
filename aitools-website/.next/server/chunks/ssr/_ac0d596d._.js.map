{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 30, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/lib/env.ts"], "sourcesContent": ["/**\n * 动态环境配置工具\n * 根据运行时环境自动判断URL配置，而不是在配置文件中写死\n */\n\n/**\n * 获取当前运行环境的基础URL\n * 支持开发环境、生产环境和部署平台的自动检测\n */\nexport function getBaseUrl(): string {\n  // 1. 优先使用明确设置的环境变量\n  if (process.env.NEXT_PUBLIC_APP_URL) {\n    return process.env.NEXT_PUBLIC_APP_URL;\n  }\n\n  // 2. 在服务器端运行时\n  if (typeof window === 'undefined') {\n    // Vercel部署环境\n    if (process.env.VERCEL_URL) {\n      return `https://${process.env.VERCEL_URL}`;\n    }\n    \n    // Netlify部署环境\n    if (process.env.NETLIFY && process.env.URL) {\n      return process.env.URL;\n    }\n    \n    // Railway部署环境\n    if (process.env.RAILWAY_STATIC_URL) {\n      return process.env.RAILWAY_STATIC_URL;\n    }\n    \n    // 其他云平台的通用环境变量\n    if (process.env.APP_URL) {\n      return process.env.APP_URL;\n    }\n    \n    // 开发环境默认值\n    const port = process.env.PORT || '3001';\n    return `http://localhost:${port}`;\n  }\n\n  // 3. 在客户端运行时\n  if (typeof window !== 'undefined') {\n    const { protocol, hostname, port } = window.location;\n    return `${protocol}//${hostname}${port ? `:${port}` : ''}`;\n  }\n\n  // 4. 兜底默认值\n  return 'http://localhost:3001';\n}\n\n/**\n * 获取API基础URL\n */\nexport function getApiBaseUrl(): string {\n  // 1. 优先使用明确设置的环境变量\n  if (process.env.NEXT_PUBLIC_API_BASE_URL) {\n    return process.env.NEXT_PUBLIC_API_BASE_URL;\n  }\n\n  // 2. 基于基础URL构建API URL\n  const baseUrl = getBaseUrl();\n  return `${baseUrl}/api`;\n}\n\n/**\n * 获取NextAuth URL\n * NextAuth需要这个URL来处理回调和重定向\n */\nexport function getNextAuthUrl(): string {\n  // 1. 优先使用明确设置的环境变量\n  if (process.env.NEXTAUTH_URL) {\n    return process.env.NEXTAUTH_URL;\n  }\n\n  // 2. 基于基础URL构建\n  return getBaseUrl();\n}\n\n/**\n * 获取当前运行环境\n */\nexport function getEnvironment(): 'development' | 'production' | 'test' {\n  return (process.env.NODE_ENV as any) || 'development';\n}\n\n/**\n * 检查是否在开发环境\n */\nexport function isDevelopment(): boolean {\n  return getEnvironment() === 'development';\n}\n\n/**\n * 检查是否在生产环境\n */\nexport function isProduction(): boolean {\n  return getEnvironment() === 'production';\n}\n\n/**\n * 获取当前端口号\n */\nexport function getCurrentPort(): string {\n  // 服务器端\n  if (typeof window === 'undefined') {\n    return process.env.PORT || '3001';\n  }\n  \n  // 客户端\n  return window.location.port || (window.location.protocol === 'https:' ? '443' : '80');\n}\n\n/**\n * 动态环境配置对象\n * 可以在应用中直接使用这些配置\n */\nexport const dynamicEnv = {\n  baseUrl: getBaseUrl(),\n  apiBaseUrl: getApiBaseUrl(),\n  nextAuthUrl: getNextAuthUrl(),\n  environment: getEnvironment(),\n  isDevelopment: isDevelopment(),\n  isProduction: isProduction(),\n  port: getCurrentPort(),\n};\n\n/**\n * 在开发环境中打印配置信息（用于调试）\n */\nif (isDevelopment() && typeof window === 'undefined') {\n  console.log('🔧 Dynamic Environment Configuration:');\n  console.log('  Base URL:', dynamicEnv.baseUrl);\n  console.log('  API Base URL:', dynamicEnv.apiBaseUrl);\n  console.log('  NextAuth URL:', dynamicEnv.nextAuthUrl);\n  console.log('  Environment:', dynamicEnv.environment);\n  console.log('  Port:', dynamicEnv.port);\n}\n"], "names": [], "mappings": "AAAA;;;CAGC,GAED;;;CAGC;;;;;;;;;;AACM,SAAS;IACd,mBAAmB;IACnB,IAAI,QAAQ,GAAG,CAAC,mBAAmB,EAAE;QACnC,OAAO,QAAQ,GAAG,CAAC,mBAAmB;IACxC;IAEA,cAAc;IACd,wCAAmC;QACjC,aAAa;QACb,IAAI,QAAQ,GAAG,CAAC,UAAU,EAAE;YAC1B,OAAO,CAAC,QAAQ,EAAE,QAAQ,GAAG,CAAC,UAAU,EAAE;QAC5C;QAEA,cAAc;QACd,IAAI,QAAQ,GAAG,CAAC,OAAO,IAAI,QAAQ,GAAG,CAAC,GAAG,EAAE;YAC1C,OAAO,QAAQ,GAAG,CAAC,GAAG;QACxB;QAEA,cAAc;QACd,IAAI,QAAQ,GAAG,CAAC,kBAAkB,EAAE;YAClC,OAAO,QAAQ,GAAG,CAAC,kBAAkB;QACvC;QAEA,eAAe;QACf,IAAI,QAAQ,GAAG,CAAC,OAAO,EAAE;YACvB,OAAO,QAAQ,GAAG,CAAC,OAAO;QAC5B;QAEA,UAAU;QACV,MAAM,OAAO,QAAQ,GAAG,CAAC,IAAI,IAAI;QACjC,OAAO,CAAC,iBAAiB,EAAE,MAAM;IACnC;;AAUF;AAKO,SAAS;IACd,mBAAmB;IACnB,IAAI,QAAQ,GAAG,CAAC,wBAAwB,EAAE;QACxC,OAAO,QAAQ,GAAG,CAAC,wBAAwB;IAC7C;IAEA,sBAAsB;IACtB,MAAM,UAAU;IAChB,OAAO,GAAG,QAAQ,IAAI,CAAC;AACzB;AAMO,SAAS;IACd,mBAAmB;IACnB,IAAI,QAAQ,GAAG,CAAC,YAAY,EAAE;QAC5B,OAAO,QAAQ,GAAG,CAAC,YAAY;IACjC;IAEA,eAAe;IACf,OAAO;AACT;AAKO,SAAS;IACd,OAAO,mDAAiC;AAC1C;AAKO,SAAS;IACd,OAAO,qBAAqB;AAC9B;AAKO,SAAS;IACd,OAAO,qBAAqB;AAC9B;AAKO,SAAS;IACd,OAAO;IACP,wCAAmC;QACjC,OAAO,QAAQ,GAAG,CAAC,IAAI,IAAI;IAC7B;;AAIF;AAMO,MAAM,aAAa;IACxB,SAAS;IACT,YAAY;IACZ,aAAa;IACb,aAAa;IACb,eAAe;IACf,cAAc;IACd,MAAM;AACR;AAEA;;CAEC,GACD,IAAI,mBAAmB,gBAAkB,aAAa;IACpD,QAAQ,GAAG,CAAC;IACZ,QAAQ,GAAG,CAAC,eAAe,WAAW,OAAO;IAC7C,QAAQ,GAAG,CAAC,mBAAmB,WAAW,UAAU;IACpD,QAAQ,GAAG,CAAC,mBAAmB,WAAW,WAAW;IACrD,QAAQ,GAAG,CAAC,kBAAkB,WAAW,WAAW;IACpD,QAAQ,GAAG,CAAC,WAAW,WAAW,IAAI;AACxC", "debugId": null}}, {"offset": {"line": 133, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/lib/api.ts"], "sourcesContent": ["// API客户端工具类\nimport { getApiBaseUrl } from './env';\n\nconst API_BASE_URL = getApiBaseUrl();\n\nexport interface ApiResponse<T = any> {\n  success: boolean;\n  data?: T;\n  error?: string;\n  message?: string;\n  details?: string[];\n}\n\nexport interface PaginationInfo {\n  currentPage: number;\n  totalPages: number;\n  totalItems: number;\n  itemsPerPage: number;\n  hasNextPage: boolean;\n  hasPrevPage: boolean;\n}\n\nexport interface Tool {\n  _id: string;\n  name: string;\n  tagline?: string;\n  description: string;\n  longDescription?: string;\n  website: string;\n  logo?: string;\n  category: string;\n  tags: string[];\n  pricing: 'free' | 'freemium' | 'paid';\n  pricingDetails?: string;\n  screenshots?: string[];\n  submittedBy: string;\n  submittedAt: string;\n  launchDate?: string; // 实际发布日期，一般等于selectedLaunchDate\n  status: 'draft' | 'pending' | 'approved' | 'rejected'; // 去掉published状态\n  reviewNotes?: string;\n  reviewedBy?: string;\n  reviewedAt?: string;\n\n  // 发布日期选择相关\n  launchDateSelected?: boolean;\n  selectedLaunchDate?: string;\n  launchOption?: 'free' | 'paid';\n\n  // 付费相关\n  paymentRequired?: boolean;\n  paymentAmount?: number;\n  paymentStatus?: 'pending' | 'completed' | 'failed' | 'refunded';\n  orderId?: string;\n  paymentMethod?: string;\n  paidAt?: string;\n\n  views: number;\n  likes: number;\n  isActive: boolean;\n  createdAt: string;\n  updatedAt: string;\n}\n\nexport interface ToolsResponse {\n  tools: Tool[];\n  pagination: PaginationInfo;\n  stats?: {\n    total: number;\n    pending: number;\n    approved: number;\n    rejected: number;\n  };\n}\n\nexport interface AdminStats {\n  overview: {\n    totalTools: number;\n    pendingTools: number;\n    approvedTools: number;\n    rejectedTools: number;\n    totalViews: number;\n    totalLikes: number;\n    recentSubmissions: number;\n    recentApprovals: number;\n    recentRejections: number;\n    avgReviewTime: number;\n  };\n  categoryStats: Array<{\n    _id: string;\n    count: number;\n    totalViews: number;\n    totalLikes: number;\n  }>;\n  topTools: Array<{\n    _id: string;\n    name: string;\n    category: string;\n    views: number;\n    likes: number;\n  }>;\n  recentActivity: Array<{\n    _id: string;\n    name: string;\n    submittedBy: string;\n    submittedAt: string;\n    status: string;\n    reviewedAt?: string;\n    reviewedBy?: string;\n  }>;\n  dailyStats: Array<{\n    date: string;\n    day: string;\n    submissions: number;\n    approvals: number;\n    rejections: number;\n  }>;\n  timeRange: string;\n}\n\nexport interface Category {\n  id: string;\n  name: string;\n  count: number;\n  totalViews: number;\n  totalLikes: number;\n}\n\nexport interface CategoriesResponse {\n  categories: Category[];\n  overview: {\n    totalTools: number;\n    totalViews: number;\n    totalLikes: number;\n  };\n}\n\nclass ApiClient {\n  private baseURL: string;\n\n  constructor(baseURL: string = API_BASE_URL) {\n    this.baseURL = baseURL;\n  }\n\n  private async request<T>(\n    endpoint: string,\n    options: RequestInit = {}\n  ): Promise<ApiResponse<T>> {\n    try {\n      const url = `${this.baseURL}${endpoint}`;\n      const config: RequestInit = {\n        headers: {\n          'Content-Type': 'application/json',\n          ...options.headers,\n        },\n        ...options,\n      };\n\n      const response = await fetch(url, config);\n      const data = await response.json();\n\n      if (!response.ok) {\n        throw new Error(data.error || `HTTP error! status: ${response.status}`);\n      }\n\n      return data;\n    } catch (error) {\n      console.error('API request failed:', error);\n      return {\n        success: false,\n        error: error instanceof Error ? error.message : '请求失败',\n      };\n    }\n  }\n\n  // 工具相关API\n  async getTools(params?: {\n    page?: number;\n    limit?: number;\n    category?: string;\n    status?: string;\n    search?: string;\n    sort?: string;\n    order?: string;\n    dateFrom?: string;\n    dateTo?: string;\n  }): Promise<ApiResponse<ToolsResponse>> {\n    const searchParams = new URLSearchParams();\n    if (params) {\n      Object.entries(params).forEach(([key, value]) => {\n        if (value !== undefined) {\n          searchParams.append(key, value.toString());\n        }\n      });\n    }\n    const query = searchParams.toString();\n    return this.request<ToolsResponse>(`/tools${query ? `?${query}` : ''}`);\n  }\n\n  async getTool(id: string): Promise<ApiResponse<Tool>> {\n    return this.request<Tool>(`/tools/${id}`);\n  }\n\n  async createTool(toolData: Partial<Tool>): Promise<ApiResponse<Tool>> {\n    return this.request<Tool>('/tools', {\n      method: 'POST',\n      body: JSON.stringify(toolData),\n    });\n  }\n\n  async updateTool(id: string, toolData: Partial<Tool>): Promise<ApiResponse<Tool>> {\n    return this.request<Tool>(`/tools/${id}`, {\n      method: 'PUT',\n      body: JSON.stringify(toolData),\n    });\n  }\n\n  async deleteTool(id: string): Promise<ApiResponse<void>> {\n    return this.request<void>(`/tools/${id}`, {\n      method: 'DELETE',\n    });\n  }\n\n  async getLikedTools(params?: {\n    page?: number;\n    limit?: number;\n  }): Promise<ApiResponse<ToolsResponse>> {\n    const searchParams = new URLSearchParams();\n    if (params) {\n      Object.entries(params).forEach(([key, value]) => {\n        if (value !== undefined) {\n          searchParams.append(key, value.toString());\n        }\n      });\n    }\n    const query = searchParams.toString();\n    return this.request<ToolsResponse>(`/user/liked-tools${query ? `?${query}` : ''}`);\n  }\n\n  // 管理员API\n  async getAdminTools(params?: {\n    page?: number;\n    limit?: number;\n    status?: string;\n    search?: string;\n    sort?: string;\n    order?: string;\n  }): Promise<ApiResponse<ToolsResponse>> {\n    const searchParams = new URLSearchParams();\n    if (params) {\n      Object.entries(params).forEach(([key, value]) => {\n        if (value !== undefined) {\n          searchParams.append(key, value.toString());\n        }\n      });\n    }\n    const query = searchParams.toString();\n    return this.request<ToolsResponse>(`/admin/tools${query ? `?${query}` : ''}`);\n  }\n\n  async approveTool(id: string, data: {\n    reviewedBy?: string;\n    reviewNotes?: string;\n    launchDate?: string;\n  }): Promise<ApiResponse<Tool>> {\n    return this.request<Tool>(`/admin/tools/${id}/approve`, {\n      method: 'POST',\n      body: JSON.stringify(data),\n    });\n  }\n\n  async rejectTool(id: string, data: {\n    reviewedBy?: string;\n    rejectReason: string;\n  }): Promise<ApiResponse<Tool>> {\n    return this.request<Tool>(`/admin/tools/${id}/reject`, {\n      method: 'POST',\n      body: JSON.stringify(data),\n    });\n  }\n\n  async getAdminStats(timeRange?: string): Promise<ApiResponse<AdminStats>> {\n    const query = timeRange ? `?timeRange=${timeRange}` : '';\n    return this.request<AdminStats>(`/admin/stats${query}`);\n  }\n\n  // 分类API\n  async getCategories(): Promise<ApiResponse<CategoriesResponse>> {\n    return this.request<CategoriesResponse>('/categories');\n  }\n}\n\n// 创建默认的API客户端实例\nexport const apiClient = new ApiClient();\n\n// 导出API客户端类以便在需要时创建新实例\nexport { ApiClient };\n"], "names": [], "mappings": "AAAA,YAAY;;;;;AACZ;;AAEA,MAAM,eAAe,CAAA,GAAA,iHAAA,CAAA,gBAAa,AAAD;AAqIjC,MAAM;IACI,QAAgB;IAExB,YAAY,UAAkB,YAAY,CAAE;QAC1C,IAAI,CAAC,OAAO,GAAG;IACjB;IAEA,MAAc,QACZ,QAAgB,EAChB,UAAuB,CAAC,CAAC,EACA;QACzB,IAAI;YACF,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,GAAG,UAAU;YACxC,MAAM,SAAsB;gBAC1B,SAAS;oBACP,gBAAgB;oBAChB,GAAG,QAAQ,OAAO;gBACpB;gBACA,GAAG,OAAO;YACZ;YAEA,MAAM,WAAW,MAAM,MAAM,KAAK;YAClC,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,KAAK,KAAK,IAAI,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;YACxE;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uBAAuB;YACrC,OAAO;gBACL,SAAS;gBACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAClD;QACF;IACF;IAEA,UAAU;IACV,MAAM,SAAS,MAUd,EAAuC;QACtC,MAAM,eAAe,IAAI;QACzB,IAAI,QAAQ;YACV,OAAO,OAAO,CAAC,QAAQ,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;gBAC1C,IAAI,UAAU,WAAW;oBACvB,aAAa,MAAM,CAAC,KAAK,MAAM,QAAQ;gBACzC;YACF;QACF;QACA,MAAM,QAAQ,aAAa,QAAQ;QACnC,OAAO,IAAI,CAAC,OAAO,CAAgB,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC,EAAE,OAAO,GAAG,IAAI;IACxE;IAEA,MAAM,QAAQ,EAAU,EAA8B;QACpD,OAAO,IAAI,CAAC,OAAO,CAAO,CAAC,OAAO,EAAE,IAAI;IAC1C;IAEA,MAAM,WAAW,QAAuB,EAA8B;QACpE,OAAO,IAAI,CAAC,OAAO,CAAO,UAAU;YAClC,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,MAAM,WAAW,EAAU,EAAE,QAAuB,EAA8B;QAChF,OAAO,IAAI,CAAC,OAAO,CAAO,CAAC,OAAO,EAAE,IAAI,EAAE;YACxC,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,MAAM,WAAW,EAAU,EAA8B;QACvD,OAAO,IAAI,CAAC,OAAO,CAAO,CAAC,OAAO,EAAE,IAAI,EAAE;YACxC,QAAQ;QACV;IACF;IAEA,MAAM,cAAc,MAGnB,EAAuC;QACtC,MAAM,eAAe,IAAI;QACzB,IAAI,QAAQ;YACV,OAAO,OAAO,CAAC,QAAQ,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;gBAC1C,IAAI,UAAU,WAAW;oBACvB,aAAa,MAAM,CAAC,KAAK,MAAM,QAAQ;gBACzC;YACF;QACF;QACA,MAAM,QAAQ,aAAa,QAAQ;QACnC,OAAO,IAAI,CAAC,OAAO,CAAgB,CAAC,iBAAiB,EAAE,QAAQ,CAAC,CAAC,EAAE,OAAO,GAAG,IAAI;IACnF;IAEA,SAAS;IACT,MAAM,cAAc,MAOnB,EAAuC;QACtC,MAAM,eAAe,IAAI;QACzB,IAAI,QAAQ;YACV,OAAO,OAAO,CAAC,QAAQ,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;gBAC1C,IAAI,UAAU,WAAW;oBACvB,aAAa,MAAM,CAAC,KAAK,MAAM,QAAQ;gBACzC;YACF;QACF;QACA,MAAM,QAAQ,aAAa,QAAQ;QACnC,OAAO,IAAI,CAAC,OAAO,CAAgB,CAAC,YAAY,EAAE,QAAQ,CAAC,CAAC,EAAE,OAAO,GAAG,IAAI;IAC9E;IAEA,MAAM,YAAY,EAAU,EAAE,IAI7B,EAA8B;QAC7B,OAAO,IAAI,CAAC,OAAO,CAAO,CAAC,aAAa,EAAE,GAAG,QAAQ,CAAC,EAAE;YACtD,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,MAAM,WAAW,EAAU,EAAE,IAG5B,EAA8B;QAC7B,OAAO,IAAI,CAAC,OAAO,CAAO,CAAC,aAAa,EAAE,GAAG,OAAO,CAAC,EAAE;YACrD,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,MAAM,cAAc,SAAkB,EAAoC;QACxE,MAAM,QAAQ,YAAY,CAAC,WAAW,EAAE,WAAW,GAAG;QACtD,OAAO,IAAI,CAAC,OAAO,CAAa,CAAC,YAAY,EAAE,OAAO;IACxD;IAEA,QAAQ;IACR,MAAM,gBAA0D;QAC9D,OAAO,IAAI,CAAC,OAAO,CAAqB;IAC1C;AACF;AAGO,MAAM,YAAY,IAAI", "debugId": null}}, {"offset": {"line": 257, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/app/%5Blocale%5D/admin/tools/%5Bid%5D/AdminToolDetailClient.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/app/[locale]/admin/tools/[id]/AdminToolDetailClient.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/app/[locale]/admin/tools/[id]/AdminToolDetailClient.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA+T,GAC5V,6FACA", "debugId": null}}, {"offset": {"line": 271, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/app/%5Blocale%5D/admin/tools/%5Bid%5D/AdminToolDetailClient.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/app/[locale]/admin/tools/[id]/AdminToolDetailClient.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/app/[locale]/admin/tools/[id]/AdminToolDetailClient.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA2S,GACxU,yEACA", "debugId": null}}, {"offset": {"line": 285, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 295, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/app/%5Blocale%5D/admin/tools/%5Bid%5D/page.tsx"], "sourcesContent": ["import React from 'react';\nimport { notFound } from 'next/navigation';\nimport { getTranslations } from 'next-intl/server';\nimport { apiClient, Tool } from '@/lib/api';\nimport { Locale } from '@/i18n/config';\nimport AdminToolDetailClient from './AdminToolDetailClient';\nimport BackButton from './BackButton';\nimport {\n  ArrowLeft,\n  ExternalLink,\n  Calendar,\n  User,\n  Tag,\n  CheckCircle,\n  XCircle,\n  Clock,\n  AlertTriangle,\n  Globe,\n  DollarSign\n} from 'lucide-react';\n\n// 服务端数据获取函数\nasync function getToolData(id: string) {\n  try {\n    const response = await apiClient.getTool(id);\n\n    if (response.success && response.data) {\n      return {\n        tool: response.data,\n        error: null\n      };\n    } else {\n      return {\n        tool: null,\n        error: response.error || 'Failed to fetch tool'\n      };\n    }\n  } catch (error) {\n    console.error('Failed to fetch tool:', error);\n    return {\n      tool: null,\n      error: 'Failed to fetch tool, please try again later'\n    };\n  }\n}\n\nexport default async function AdminToolDetailPage({\n  params\n}: {\n  params: Promise<{ locale: Locale; id: string }>\n}) {\n  const { locale, id } = await params;\n\n  // 验证ID格式\n  if (!id || id.length !== 24) {\n    notFound();\n  }\n\n  // 获取翻译\n  const t = await getTranslations('admin');\n  const tCategories = await getTranslations('categories');\n\n  // 获取工具数据\n  const { tool, error } = await getToolData(id);\n\n  if (error || !tool) {\n    return (\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        <div className=\"text-center py-12\">\n          <AlertTriangle className=\"mx-auto h-12 w-12 text-gray-400\" />\n          <h3 className=\"mt-2 text-sm font-medium text-gray-900\">\n            {error || t('errors.tool_not_found')}\n          </h3>\n        </div>\n      </div>\n    );\n  }\n\n  // 格式化日期的辅助函数\n  const formatDate = (dateString: string) => {\n    return new Date(dateString).toLocaleDateString(locale === 'zh' ? 'zh-CN' : 'en-US', {\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric',\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  };\n\n  // 获取状态徽章的辅助函数\n  const getStatusBadge = (tool: { status: string; launchDate?: string }) => {\n    // Check if published: approved status and launchDate has passed\n    const isPublished = tool.status === 'approved' && tool.launchDate && new Date(tool.launchDate) <= new Date();\n\n    if (isPublished) {\n      return (\n        <span className=\"inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800\">\n          <CheckCircle className=\"w-4 h-4 mr-2\" />\n          {t('status_labels.published')}\n        </span>\n      );\n    }\n\n    switch (tool.status) {\n      case 'pending':\n        return (\n          <span className=\"inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-yellow-100 text-yellow-800\">\n            <Clock className=\"w-4 h-4 mr-2\" />\n            {t('status_labels.pending')}\n          </span>\n        );\n      case 'approved':\n        return (\n          <span className=\"inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800\">\n            <CheckCircle className=\"w-4 h-4 mr-2\" />\n            {t('status_labels.approved')}\n          </span>\n        );\n\n      case 'rejected':\n        return (\n          <span className=\"inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-red-100 text-red-800\">\n            <XCircle className=\"w-4 h-4 mr-2\" />\n            {t('status_labels.rejected')}\n          </span>\n        );\n      default:\n        return null;\n    }\n  };\n\n  // Helper function to get status badge\n  const getStatusBadge = (tool: { status: string; launchDate?: string }) => {\n    // Check if published: approved status and launchDate has passed\n    const isPublished = tool.status === 'approved' && tool.launchDate && new Date(tool.launchDate) <= new Date();\n\n    if (isPublished) {\n      return (\n        <span className=\"inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800\">\n          <CheckCircle className=\"w-4 h-4 mr-2\" />\n          {t('status_labels.published')}\n        </span>\n      );\n    }\n\n    switch (tool.status) {\n      case 'pending':\n        return (\n          <span className=\"inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-yellow-100 text-yellow-800\">\n            <Clock className=\"w-4 h-4 mr-2\" />\n            {t('status_labels.pending')}\n          </span>\n        );\n      case 'approved':\n        return (\n          <span className=\"inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800\">\n            <CheckCircle className=\"w-4 h-4 mr-2\" />\n            {t('status_labels.approved')}\n          </span>\n        );\n      case 'rejected':\n        return (\n          <span className=\"inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-red-100 text-red-800\">\n            <XCircle className=\"w-4 h-4 mr-2\" />\n            {t('status_labels.rejected')}\n          </span>\n        );\n      default:\n        return null;\n    }\n  };\n\n  return (\n    <div className=\"max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n      {/* 客户端组件处理交互功能 */}\n      <AdminToolDetailClient\n        tool={tool}\n        locale={locale}\n      />\n\n      {/* Back Button */}\n      <div className=\"mb-8\">\n        <button\n          onClick={() => window.history.back()}\n          className=\"flex items-center text-gray-600 hover:text-gray-900 mb-4 transition-colors\"\n        >\n          <ArrowLeft className=\"w-4 h-4 mr-2\" />\n          {t('actions.back_to_review')}\n        </button>\n\n        <div className=\"flex items-start justify-between\">\n          <div className=\"flex items-start space-x-6\">\n            <img\n              src={tool.logo}\n              alt={tool.name}\n              className=\"w-24 h-24 rounded-xl object-cover border border-gray-200 shadow-sm\"\n            />\n            <div>\n              <div className=\"flex items-center space-x-3 mb-2\">\n                <h1 className=\"text-3xl font-bold text-gray-900\">{tool.name}</h1>\n                {getStatusBadge(tool)}\n              </div>\n              <div className=\"flex items-center space-x-4 text-sm text-gray-600 mb-4\">\n                <span className=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800\">\n                  {tCategories(`category_names.${tool.category}`) || tool.category}\n                </span>\n                <span className=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800\">\n                  <DollarSign className=\"w-3 h-3 mr-1\" />\n                  {t(`pricing_labels.${tool.pricing}`) || tool.pricing}\n                </span>\n                <a\n                  href={tool.website}\n                  target=\"_blank\"\n                  rel=\"noopener noreferrer\"\n                  className=\"inline-flex items-center text-blue-600 hover:text-blue-800 transition-colors\"\n                >\n                  <Globe className=\"w-4 h-4 mr-1\" />\n                  {t('actions.visit_website')}\n                  <ExternalLink className=\"w-3 h-3 ml-1\" />\n                </a>\n              </div>\n              <p className=\"text-gray-600 max-w-3xl\">{tool.tagline}</p>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* 静态内容 - 服务端渲染 */}\n      <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-8 mt-8\">\n\n        {/* Main Content */}\n        <div className=\"lg:col-span-2 space-y-8\">\n          {/* Description */}\n          <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n            <h2 className=\"text-xl font-semibold text-gray-900 mb-4\">{t('sections.tool_description')}</h2>\n            <div className=\"prose max-w-none\">\n              <p className=\"text-gray-700 leading-relaxed\">\n                {tool.description}\n              </p>\n              {tool.longDescription && (\n                <div className=\"mt-4\">\n                  <h3 className=\"text-lg font-medium text-gray-900 mb-2\">{t('sections.detailed_description')}</h3>\n                  <p className=\"text-gray-700 leading-relaxed\">\n                    {tool.longDescription}\n                  </p>\n                </div>\n              )}\n            </div>\n          </div>\n\n          {/* Tags */}\n          <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n            <h2 className=\"text-xl font-semibold text-gray-900 mb-4\">{t('sections.tags')}</h2>\n            <div className=\"flex flex-wrap gap-2\">\n              {tool.tags.map((tag, index) => (\n                <span\n                  key={index}\n                  className=\"inline-flex items-center px-3 py-1 rounded-md text-sm font-medium bg-blue-100 text-blue-800\"\n                >\n                  <Tag className=\"w-3 h-3 mr-1\" />\n                  {tag}\n                </span>\n              ))}\n            </div>\n          </div>\n\n          {/* Screenshots */}\n          {tool.screenshots && tool.screenshots.length > 0 && (\n            <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n              <h2 className=\"text-xl font-semibold text-gray-900 mb-4\">{t('sections.screenshot_preview')}</h2>\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                {tool.screenshots.map((screenshot, index) => (\n                  <img\n                    key={index}\n                    src={screenshot}\n                    alt={`${tool.name} ${t('sections.screenshot_preview')} ${index + 1}`}\n                    className=\"w-full h-48 object-cover rounded-lg border border-gray-200\"\n                  />\n                ))}\n              </div>\n            </div>\n          )}\n        </div>\n\n        {/* Sidebar */}\n        <div className=\"space-y-6\">\n          {/* Submission Info */}\n          <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n            <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">{t('sections.submission_info')}</h3>\n            <div className=\"space-y-4\">\n              <div className=\"flex items-center\">\n                <User className=\"w-5 h-5 text-gray-400 mr-3\" />\n                <div>\n                  <div className=\"text-sm font-medium text-gray-900\">{tool.submittedBy}</div>\n                  <div className=\"text-sm text-gray-500\">{t('fields.submitter_id')}</div>\n                </div>\n              </div>\n\n              <div className=\"flex items-center\">\n                <Calendar className=\"w-5 h-5 text-gray-400 mr-3\" />\n                <div>\n                  <div className=\"text-sm font-medium text-gray-900\">{formatDate(tool.submittedAt)}</div>\n                  <div className=\"text-sm text-gray-500\">{t('fields.submission_time')}</div>\n                </div>\n              </div>\n\n              {tool.selectedLaunchDate && (\n                <div className=\"flex items-center\">\n                  <Clock className=\"w-5 h-5 text-gray-400 mr-3\" />\n                  <div>\n                    <div className=\"text-sm font-medium text-gray-900\">\n                      {new Date(tool.selectedLaunchDate).toLocaleDateString(locale === 'zh' ? 'zh-CN' : 'en-US')}\n                    </div>\n                    <div className=\"text-sm text-gray-500\">{t('fields.selected_launch_date')}</div>\n                  </div>\n                </div>\n              )}\n\n              {tool.launchDate && (\n                <div className=\"flex items-center\">\n                  <CheckCircle className=\"w-5 h-5 text-gray-400 mr-3\" />\n                  <div>\n                    <div className=\"text-sm font-medium text-gray-900\">\n                      {new Date(tool.launchDate).toLocaleDateString(locale === 'zh' ? 'zh-CN' : 'en-US')}\n                    </div>\n                    <div className=\"text-sm text-gray-500\">{t('fields.actual_launch_date')}</div>\n                  </div>\n                </div>\n              )}\n            </div>\n          </div>\n\n          {/* Review Guidelines */}\n          <div className=\"bg-blue-50 border border-blue-200 rounded-lg p-6\">\n            <div className=\"flex items-start\">\n              <AlertTriangle className=\"w-5 h-5 text-blue-600 mr-3 mt-0.5 flex-shrink-0\" />\n              <div>\n                <h3 className=\"text-sm font-medium text-blue-900 mb-2\">{t('sections.review_guidelines')}</h3>\n                <ul className=\"text-sm text-blue-800 space-y-1\">\n                  <li>• {t('guidelines.verify_website')}</li>\n                  <li>• {t('guidelines.check_description')}</li>\n                  <li>• {t('guidelines.confirm_category')}</li>\n                  <li>• {t('guidelines.evaluate_quality')}</li>\n                  <li>• {t('guidelines.check_duplicates')}</li>\n                </ul>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AACA;AAAA;AACA;AACA;AAEA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;AAcA,YAAY;AACZ,eAAe,YAAY,EAAU;IACnC,IAAI;QACF,MAAM,WAAW,MAAM,iHAAA,CAAA,YAAS,CAAC,OAAO,CAAC;QAEzC,IAAI,SAAS,OAAO,IAAI,SAAS,IAAI,EAAE;YACrC,OAAO;gBACL,MAAM,SAAS,IAAI;gBACnB,OAAO;YACT;QACF,OAAO;YACL,OAAO;gBACL,MAAM;gBACN,OAAO,SAAS,KAAK,IAAI;YAC3B;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yBAAyB;QACvC,OAAO;YACL,MAAM;YACN,OAAO;QACT;IACF;AACF;AAEe,eAAe,oBAAoB,EAChD,MAAM,EAGP;IACC,MAAM,EAAE,MAAM,EAAE,EAAE,EAAE,GAAG,MAAM;IAE7B,SAAS;IACT,IAAI,CAAC,MAAM,GAAG,MAAM,KAAK,IAAI;QAC3B,CAAA,GAAA,qLAAA,CAAA,WAAQ,AAAD;IACT;IAEA,OAAO;IACP,MAAM,IAAI,MAAM,CAAA,GAAA,wPAAA,CAAA,kBAAe,AAAD,EAAE;IAChC,MAAM,cAAc,MAAM,CAAA,GAAA,wPAAA,CAAA,kBAAe,AAAD,EAAE;IAE1C,SAAS;IACT,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,YAAY;IAE1C,IAAI,SAAS,CAAC,MAAM;QAClB,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,wNAAA,CAAA,gBAAa;wBAAC,WAAU;;;;;;kCACzB,8OAAC;wBAAG,WAAU;kCACX,SAAS,EAAE;;;;;;;;;;;;;;;;;IAKtB;IAEA,aAAa;IACb,MAAM,aAAa,CAAC;QAClB,OAAO,IAAI,KAAK,YAAY,kBAAkB,CAAC,WAAW,OAAO,UAAU,SAAS;YAClF,MAAM;YACN,OAAO;YACP,KAAK;YACL,MAAM;YACN,QAAQ;QACV;IACF;IAEA,cAAc;IACd,MAAM,iBAAiB,CAAC;QACtB,gEAAgE;QAChE,MAAM,cAAc,KAAK,MAAM,KAAK,cAAc,KAAK,UAAU,IAAI,IAAI,KAAK,KAAK,UAAU,KAAK,IAAI;QAEtG,IAAI,aAAa;YACf,qBACE,8OAAC;gBAAK,WAAU;;kCACd,8OAAC,2NAAA,CAAA,cAAW;wBAAC,WAAU;;;;;;oBACtB,EAAE;;;;;;;QAGT;QAEA,OAAQ,KAAK,MAAM;YACjB,KAAK;gBACH,qBACE,8OAAC;oBAAK,WAAU;;sCACd,8OAAC,oMAAA,CAAA,QAAK;4BAAC,WAAU;;;;;;wBAChB,EAAE;;;;;;;YAGT,KAAK;gBACH,qBACE,8OAAC;oBAAK,WAAU;;sCACd,8OAAC,2NAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;wBACtB,EAAE;;;;;;;YAIT,KAAK;gBACH,qBACE,8OAAC;oBAAK,WAAU;;sCACd,8OAAC,4MAAA,CAAA,UAAO;4BAAC,WAAU;;;;;;wBAClB,EAAE;;;;;;;YAGT;gBACE,OAAO;QACX;IACF;IAEA,sCAAsC;IACtC,MAAM,iBAAiB,CAAC;QACtB,gEAAgE;QAChE,MAAM,cAAc,KAAK,MAAM,KAAK,cAAc,KAAK,UAAU,IAAI,IAAI,KAAK,KAAK,UAAU,KAAK,IAAI;QAEtG,IAAI,aAAa;YACf,qBACE,8OAAC;gBAAK,WAAU;;kCACd,8OAAC,2NAAA,CAAA,cAAW;wBAAC,WAAU;;;;;;oBACtB,EAAE;;;;;;;QAGT;QAEA,OAAQ,KAAK,MAAM;YACjB,KAAK;gBACH,qBACE,8OAAC;oBAAK,WAAU;;sCACd,8OAAC,oMAAA,CAAA,QAAK;4BAAC,WAAU;;;;;;wBAChB,EAAE;;;;;;;YAGT,KAAK;gBACH,qBACE,8OAAC;oBAAK,WAAU;;sCACd,8OAAC,2NAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;wBACtB,EAAE;;;;;;;YAGT,KAAK;gBACH,qBACE,8OAAC;oBAAK,WAAU;;sCACd,8OAAC,4MAAA,CAAA,UAAO;4BAAC,WAAU;;;;;;wBAClB,EAAE;;;;;;;YAGT;gBACE,OAAO;QACX;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC,8KAAA,CAAA,UAAqB;gBACpB,MAAM;gBACN,QAAQ;;;;;;0BAIV,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBACC,SAAS,IAAM,OAAO,OAAO,CAAC,IAAI;wBAClC,WAAU;;0CAEV,8OAAC,gNAAA,CAAA,YAAS;gCAAC,WAAU;;;;;;4BACpB,EAAE;;;;;;;kCAGL,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,KAAK,KAAK,IAAI;oCACd,KAAK,KAAK,IAAI;oCACd,WAAU;;;;;;8CAEZ,8OAAC;;sDACC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAAoC,KAAK,IAAI;;;;;;gDAC1D,eAAe;;;;;;;sDAElB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DACb,YAAY,CAAC,eAAe,EAAE,KAAK,QAAQ,EAAE,KAAK,KAAK,QAAQ;;;;;;8DAElE,8OAAC;oDAAK,WAAU;;sEACd,8OAAC,kNAAA,CAAA,aAAU;4DAAC,WAAU;;;;;;wDACrB,EAAE,CAAC,eAAe,EAAE,KAAK,OAAO,EAAE,KAAK,KAAK,OAAO;;;;;;;8DAEtD,8OAAC;oDACC,MAAM,KAAK,OAAO;oDAClB,QAAO;oDACP,KAAI;oDACJ,WAAU;;sEAEV,8OAAC,oMAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;wDAChB,EAAE;sEACH,8OAAC,sNAAA,CAAA,eAAY;4DAAC,WAAU;;;;;;;;;;;;;;;;;;sDAG5B,8OAAC;4CAAE,WAAU;sDAA2B,KAAK,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAO5D,8OAAC;gBAAI,WAAU;;kCAGb,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAA4C,EAAE;;;;;;kDAC5D,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;0DACV,KAAK,WAAW;;;;;;4CAElB,KAAK,eAAe,kBACnB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAA0C,EAAE;;;;;;kEAC1D,8OAAC;wDAAE,WAAU;kEACV,KAAK,eAAe;;;;;;;;;;;;;;;;;;;;;;;;0CAQ/B,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAA4C,EAAE;;;;;;kDAC5D,8OAAC;wCAAI,WAAU;kDACZ,KAAK,IAAI,CAAC,GAAG,CAAC,CAAC,KAAK,sBACnB,8OAAC;gDAEC,WAAU;;kEAEV,8OAAC,gMAAA,CAAA,MAAG;wDAAC,WAAU;;;;;;oDACd;;+CAJI;;;;;;;;;;;;;;;;4BAWZ,KAAK,WAAW,IAAI,KAAK,WAAW,CAAC,MAAM,GAAG,mBAC7C,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAA4C,EAAE;;;;;;kDAC5D,8OAAC;wCAAI,WAAU;kDACZ,KAAK,WAAW,CAAC,GAAG,CAAC,CAAC,YAAY,sBACjC,8OAAC;gDAEC,KAAK;gDACL,KAAK,GAAG,KAAK,IAAI,CAAC,CAAC,EAAE,EAAE,+BAA+B,CAAC,EAAE,QAAQ,GAAG;gDACpE,WAAU;+CAHL;;;;;;;;;;;;;;;;;;;;;;kCAYjB,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAA4C,EAAE;;;;;;kDAC5D,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;kEAChB,8OAAC;;0EACC,8OAAC;gEAAI,WAAU;0EAAqC,KAAK,WAAW;;;;;;0EACpE,8OAAC;gEAAI,WAAU;0EAAyB,EAAE;;;;;;;;;;;;;;;;;;0DAI9C,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,0MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;kEACpB,8OAAC;;0EACC,8OAAC;gEAAI,WAAU;0EAAqC,WAAW,KAAK,WAAW;;;;;;0EAC/E,8OAAC;gEAAI,WAAU;0EAAyB,EAAE;;;;;;;;;;;;;;;;;;4CAI7C,KAAK,kBAAkB,kBACtB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,oMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;kEACjB,8OAAC;;0EACC,8OAAC;gEAAI,WAAU;0EACZ,IAAI,KAAK,KAAK,kBAAkB,EAAE,kBAAkB,CAAC,WAAW,OAAO,UAAU;;;;;;0EAEpF,8OAAC;gEAAI,WAAU;0EAAyB,EAAE;;;;;;;;;;;;;;;;;;4CAK/C,KAAK,UAAU,kBACd,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,2NAAA,CAAA,cAAW;wDAAC,WAAU;;;;;;kEACvB,8OAAC;;0EACC,8OAAC;gEAAI,WAAU;0EACZ,IAAI,KAAK,KAAK,UAAU,EAAE,kBAAkB,CAAC,WAAW,OAAO,UAAU;;;;;;0EAE5E,8OAAC;gEAAI,WAAU;0EAAyB,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAQpD,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,wNAAA,CAAA,gBAAa;4CAAC,WAAU;;;;;;sDACzB,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAA0C,EAAE;;;;;;8DAC1D,8OAAC;oDAAG,WAAU;;sEACZ,8OAAC;;gEAAG;gEAAG,EAAE;;;;;;;sEACT,8OAAC;;gEAAG;gEAAG,EAAE;;;;;;;sEACT,8OAAC;;gEAAG;gEAAG,EAAE;;;;;;;sEACT,8OAAC;;gEAAG;gEAAG,EAAE;;;;;;;sEACT,8OAAC;;gEAAG;gEAAG,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS3B", "debugId": null}}]}