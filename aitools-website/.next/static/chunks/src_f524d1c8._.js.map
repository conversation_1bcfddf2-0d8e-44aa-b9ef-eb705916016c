{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/components/Layout.tsx"], "sourcesContent": ["// This file is deprecated - Footer functionality moved to layout/Footer.tsx\n"], "names": [], "mappings": "AAAA,4EAA4E", "debugId": null}}, {"offset": {"line": 16, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/components/LoadingSpinner.tsx"], "sourcesContent": ["'use client';\n\ninterface LoadingSpinnerProps {\n  size?: 'sm' | 'md' | 'lg';\n  className?: string;\n}\n\nexport default function LoadingSpinner({ size = 'md', className = '' }: LoadingSpinnerProps) {\n  const sizeClasses = {\n    sm: 'w-4 h-4',\n    md: 'w-8 h-8',\n    lg: 'w-12 h-12'\n  };\n\n  return (\n    <div className={`flex justify-center items-center ${className}`}>\n      <div className={`${sizeClasses[size]} border-4 border-gray-200 border-t-blue-600 rounded-full animate-spin`}></div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;;AAOe,SAAS,eAAe,EAAE,OAAO,IAAI,EAAE,YAAY,EAAE,EAAuB;IACzF,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,6LAAC;QAAI,WAAW,CAAC,iCAAiC,EAAE,WAAW;kBAC7D,cAAA,6LAAC;YAAI,WAAW,GAAG,WAAW,CAAC,KAAK,CAAC,qEAAqE,CAAC;;;;;;;;;;;AAGjH;KAZwB", "debugId": null}}, {"offset": {"line": 55, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/components/ErrorMessage.tsx"], "sourcesContent": ["'use client';\n\nimport { AlertCircle, X } from 'lucide-react';\n\ninterface ErrorMessageProps {\n  message: string;\n  onClose?: () => void;\n  className?: string;\n}\n\nexport default function ErrorMessage({ message, onClose, className = '' }: ErrorMessageProps) {\n  return (\n    <div className={`bg-red-50 border border-red-200 rounded-lg p-4 ${className}`}>\n      <div className=\"flex items-start\">\n        <AlertCircle className=\"w-5 h-5 text-red-500 mt-0.5 mr-3 flex-shrink-0\" />\n        <div className=\"flex-1\">\n          <p className=\"text-red-800 text-sm\">{message}</p>\n        </div>\n        {onClose && (\n          <button\n            onClick={onClose}\n            className=\"ml-3 text-red-400 hover:text-red-600 transition-colors\"\n          >\n            <X className=\"w-4 h-4\" />\n          </button>\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAFA;;;AAUe,SAAS,aAAa,EAAE,OAAO,EAAE,OAAO,EAAE,YAAY,EAAE,EAAqB;IAC1F,qBACE,6LAAC;QAAI,WAAW,CAAC,+CAA+C,EAAE,WAAW;kBAC3E,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC,uNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;8BACvB,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAE,WAAU;kCAAwB;;;;;;;;;;;gBAEtC,yBACC,6LAAC;oBACC,SAAS;oBACT,WAAU;8BAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;wBAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;AAMzB;KAnBwB", "debugId": null}}, {"offset": {"line": 131, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/components/SuccessMessage.tsx"], "sourcesContent": ["'use client';\n\nimport { CheckCircle, X } from 'lucide-react';\n\ninterface SuccessMessageProps {\n  message: string;\n  onClose?: () => void;\n  className?: string;\n}\n\nexport default function SuccessMessage({ message, onClose, className = '' }: SuccessMessageProps) {\n  return (\n    <div className={`bg-green-50 border border-green-200 rounded-lg p-4 ${className}`}>\n      <div className=\"flex items-start\">\n        <CheckCircle className=\"w-5 h-5 text-green-500 mt-0.5 mr-3 flex-shrink-0\" />\n        <div className=\"flex-1\">\n          <p className=\"text-green-800 text-sm\">{message}</p>\n        </div>\n        {onClose && (\n          <button\n            onClick={onClose}\n            className=\"ml-3 text-green-400 hover:text-green-600 transition-colors\"\n          >\n            <X className=\"w-4 h-4\" />\n          </button>\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAFA;;;AAUe,SAAS,eAAe,EAAE,OAAO,EAAE,OAAO,EAAE,YAAY,EAAE,EAAuB;IAC9F,qBACE,6LAAC;QAAI,WAAW,CAAC,mDAAmD,EAAE,WAAW;kBAC/E,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC,8NAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;8BACvB,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAE,WAAU;kCAA0B;;;;;;;;;;;gBAExC,yBACC,6LAAC;oBACC,SAAS;oBACT,WAAU;8BAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;wBAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;AAMzB;KAnBwB", "debugId": null}}, {"offset": {"line": 207, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/lib/env.ts"], "sourcesContent": ["/**\n * 动态环境配置工具\n * 根据运行时环境自动判断URL配置，而不是在配置文件中写死\n */\n\n/**\n * 获取当前运行环境的基础URL\n * 支持开发环境、生产环境和部署平台的自动检测\n */\nexport function getBaseUrl(): string {\n  // 1. 优先使用明确设置的环境变量\n  if (process.env.NEXT_PUBLIC_APP_URL) {\n    return process.env.NEXT_PUBLIC_APP_URL;\n  }\n\n  // 2. 在服务器端运行时\n  if (typeof window === 'undefined') {\n    // Vercel部署环境\n    if (process.env.VERCEL_URL) {\n      return `https://${process.env.VERCEL_URL}`;\n    }\n    \n    // Netlify部署环境\n    if (process.env.NETLIFY && process.env.URL) {\n      return process.env.URL;\n    }\n    \n    // Railway部署环境\n    if (process.env.RAILWAY_STATIC_URL) {\n      return process.env.RAILWAY_STATIC_URL;\n    }\n    \n    // 其他云平台的通用环境变量\n    if (process.env.APP_URL) {\n      return process.env.APP_URL;\n    }\n    \n    // 开发环境默认值\n    const port = process.env.PORT || '3001';\n    return `http://localhost:${port}`;\n  }\n\n  // 3. 在客户端运行时\n  if (typeof window !== 'undefined') {\n    const { protocol, hostname, port } = window.location;\n    return `${protocol}//${hostname}${port ? `:${port}` : ''}`;\n  }\n\n  // 4. 兜底默认值\n  return 'http://localhost:3001';\n}\n\n/**\n * 获取API基础URL\n */\nexport function getApiBaseUrl(): string {\n  // 1. 优先使用明确设置的环境变量\n  if (process.env.NEXT_PUBLIC_API_BASE_URL) {\n    return process.env.NEXT_PUBLIC_API_BASE_URL;\n  }\n\n  // 2. 基于基础URL构建API URL\n  const baseUrl = getBaseUrl();\n  return `${baseUrl}/api`;\n}\n\n/**\n * 获取NextAuth URL\n * NextAuth需要这个URL来处理回调和重定向\n */\nexport function getNextAuthUrl(): string {\n  // 1. 优先使用明确设置的环境变量\n  if (process.env.NEXTAUTH_URL) {\n    return process.env.NEXTAUTH_URL;\n  }\n\n  // 2. 基于基础URL构建\n  return getBaseUrl();\n}\n\n/**\n * 获取当前运行环境\n */\nexport function getEnvironment(): 'development' | 'production' | 'test' {\n  return (process.env.NODE_ENV as any) || 'development';\n}\n\n/**\n * 检查是否在开发环境\n */\nexport function isDevelopment(): boolean {\n  return getEnvironment() === 'development';\n}\n\n/**\n * 检查是否在生产环境\n */\nexport function isProduction(): boolean {\n  return getEnvironment() === 'production';\n}\n\n/**\n * 获取当前端口号\n */\nexport function getCurrentPort(): string {\n  // 服务器端\n  if (typeof window === 'undefined') {\n    return process.env.PORT || '3001';\n  }\n  \n  // 客户端\n  return window.location.port || (window.location.protocol === 'https:' ? '443' : '80');\n}\n\n/**\n * 动态环境配置对象\n * 可以在应用中直接使用这些配置\n */\nexport const dynamicEnv = {\n  baseUrl: getBaseUrl(),\n  apiBaseUrl: getApiBaseUrl(),\n  nextAuthUrl: getNextAuthUrl(),\n  environment: getEnvironment(),\n  isDevelopment: isDevelopment(),\n  isProduction: isProduction(),\n  port: getCurrentPort(),\n};\n\n/**\n * 在开发环境中打印配置信息（用于调试）\n */\nif (isDevelopment() && typeof window === 'undefined') {\n  console.log('🔧 Dynamic Environment Configuration:');\n  console.log('  Base URL:', dynamicEnv.baseUrl);\n  console.log('  API Base URL:', dynamicEnv.apiBaseUrl);\n  console.log('  NextAuth URL:', dynamicEnv.nextAuthUrl);\n  console.log('  Environment:', dynamicEnv.environment);\n  console.log('  Port:', dynamicEnv.port);\n}\n"], "names": [], "mappings": "AAAA;;;CAGC,GAED;;;CAGC;;;;;;;;;;AAGK;AAFC,SAAS;IACd,mBAAmB;IACnB,IAAI,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,mBAAmB,EAAE;QACnC,OAAO,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,mBAAmB;IACxC;IAEA,cAAc;IACd,uCAAmC;;IAwBnC;IAEA,aAAa;IACb,wCAAmC;QACjC,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,IAAI,EAAE,GAAG,OAAO,QAAQ;QACpD,OAAO,GAAG,SAAS,EAAE,EAAE,WAAW,OAAO,CAAC,CAAC,EAAE,MAAM,GAAG,IAAI;IAC5D;;AAIF;AAKO,SAAS;IACd,mBAAmB;IACnB,IAAI,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,wBAAwB,EAAE;QACxC,OAAO,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,wBAAwB;IAC7C;IAEA,sBAAsB;IACtB,MAAM,UAAU;IAChB,OAAO,GAAG,QAAQ,IAAI,CAAC;AACzB;AAMO,SAAS;IACd,mBAAmB;IACnB,IAAI,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,YAAY,EAAE;QAC5B,OAAO,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,YAAY;IACjC;IAEA,eAAe;IACf,OAAO;AACT;AAKO,SAAS;IACd,OAAO,mDAAiC;AAC1C;AAKO,SAAS;IACd,OAAO,qBAAqB;AAC9B;AAKO,SAAS;IACd,OAAO,qBAAqB;AAC9B;AAKO,SAAS;IACd,OAAO;IACP,uCAAmC;;IAEnC;IAEA,MAAM;IACN,OAAO,OAAO,QAAQ,CAAC,IAAI,IAAI,CAAC,OAAO,QAAQ,CAAC,QAAQ,KAAK,WAAW,QAAQ,IAAI;AACtF;AAMO,MAAM,aAAa;IACxB,SAAS;IACT,YAAY;IACZ,aAAa;IACb,aAAa;IACb,eAAe;IACf,cAAc;IACd,MAAM;AACR;AAEA;;CAEC,GACD,IAAI,mBAAmB,aAAkB,aAAa;;AAOtD", "debugId": null}}, {"offset": {"line": 297, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/lib/api.ts"], "sourcesContent": ["// API客户端工具类\nimport { getApiBaseUrl } from './env';\n\nconst API_BASE_URL = getApiBaseUrl();\n\nexport interface ApiResponse<T = any> {\n  success: boolean;\n  data?: T;\n  error?: string;\n  message?: string;\n  details?: string[];\n}\n\nexport interface PaginationInfo {\n  currentPage: number;\n  totalPages: number;\n  totalItems: number;\n  itemsPerPage: number;\n  hasNextPage: boolean;\n  hasPrevPage: boolean;\n}\n\nexport interface Tool {\n  _id: string;\n  name: string;\n  tagline?: string;\n  description: string;\n  longDescription?: string;\n  website: string;\n  logo?: string;\n  category: string;\n  tags: string[];\n  pricing: 'free' | 'freemium' | 'paid';\n  pricingDetails?: string;\n  screenshots?: string[];\n  submittedBy: string;\n  submittedAt: string;\n  launchDate?: string; // 实际发布日期，一般等于selectedLaunchDate\n  status: 'draft' | 'pending' | 'approved' | 'rejected'; // 去掉published状态\n  reviewNotes?: string;\n  reviewedBy?: string;\n  reviewedAt?: string;\n\n  // 发布日期选择相关\n  launchDateSelected?: boolean;\n  selectedLaunchDate?: string;\n  launchOption?: 'free' | 'paid';\n\n  // 付费相关\n  paymentRequired?: boolean;\n  paymentAmount?: number;\n  paymentStatus?: 'pending' | 'completed' | 'failed' | 'refunded';\n  orderId?: string;\n  paymentMethod?: string;\n  paidAt?: string;\n\n  views: number;\n  likes: number;\n  isActive: boolean;\n  createdAt: string;\n  updatedAt: string;\n}\n\nexport interface ToolsResponse {\n  tools: Tool[];\n  pagination: PaginationInfo;\n  stats?: {\n    total: number;\n    pending: number;\n    approved: number;\n    rejected: number;\n  };\n}\n\nexport interface AdminStats {\n  overview: {\n    totalTools: number;\n    pendingTools: number;\n    approvedTools: number;\n    rejectedTools: number;\n    totalViews: number;\n    totalLikes: number;\n    recentSubmissions: number;\n    recentApprovals: number;\n    recentRejections: number;\n    avgReviewTime: number;\n  };\n  categoryStats: Array<{\n    _id: string;\n    count: number;\n    totalViews: number;\n    totalLikes: number;\n  }>;\n  topTools: Array<{\n    _id: string;\n    name: string;\n    category: string;\n    views: number;\n    likes: number;\n  }>;\n  recentActivity: Array<{\n    _id: string;\n    name: string;\n    submittedBy: string;\n    submittedAt: string;\n    status: string;\n    reviewedAt?: string;\n    reviewedBy?: string;\n  }>;\n  dailyStats: Array<{\n    date: string;\n    day: string;\n    submissions: number;\n    approvals: number;\n    rejections: number;\n  }>;\n  timeRange: string;\n}\n\nexport interface Category {\n  id: string;\n  name: string;\n  count: number;\n  totalViews: number;\n  totalLikes: number;\n}\n\nexport interface CategoriesResponse {\n  categories: Category[];\n  overview: {\n    totalTools: number;\n    totalViews: number;\n    totalLikes: number;\n  };\n}\n\nclass ApiClient {\n  private baseURL: string;\n\n  constructor(baseURL: string = API_BASE_URL) {\n    this.baseURL = baseURL;\n  }\n\n  private async request<T>(\n    endpoint: string,\n    options: RequestInit = {}\n  ): Promise<ApiResponse<T>> {\n    try {\n      const url = `${this.baseURL}${endpoint}`;\n      const config: RequestInit = {\n        headers: {\n          'Content-Type': 'application/json',\n          ...options.headers,\n        },\n        ...options,\n      };\n\n      const response = await fetch(url, config);\n      const data = await response.json();\n\n      if (!response.ok) {\n        throw new Error(data.error || `HTTP error! status: ${response.status}`);\n      }\n\n      return data;\n    } catch (error) {\n      console.error('API request failed:', error);\n      return {\n        success: false,\n        error: error instanceof Error ? error.message : '请求失败',\n      };\n    }\n  }\n\n  // 工具相关API\n  async getTools(params?: {\n    page?: number;\n    limit?: number;\n    category?: string;\n    status?: string;\n    search?: string;\n    sort?: string;\n    order?: string;\n    dateFrom?: string;\n    dateTo?: string;\n  }): Promise<ApiResponse<ToolsResponse>> {\n    const searchParams = new URLSearchParams();\n    if (params) {\n      Object.entries(params).forEach(([key, value]) => {\n        if (value !== undefined) {\n          searchParams.append(key, value.toString());\n        }\n      });\n    }\n    const query = searchParams.toString();\n    return this.request<ToolsResponse>(`/tools${query ? `?${query}` : ''}`);\n  }\n\n  async getTool(id: string): Promise<ApiResponse<Tool>> {\n    return this.request<Tool>(`/tools/${id}`);\n  }\n\n  async createTool(toolData: Partial<Tool>): Promise<ApiResponse<Tool>> {\n    return this.request<Tool>('/tools', {\n      method: 'POST',\n      body: JSON.stringify(toolData),\n    });\n  }\n\n  async updateTool(id: string, toolData: Partial<Tool>): Promise<ApiResponse<Tool>> {\n    return this.request<Tool>(`/tools/${id}`, {\n      method: 'PUT',\n      body: JSON.stringify(toolData),\n    });\n  }\n\n  async deleteTool(id: string): Promise<ApiResponse<void>> {\n    return this.request<void>(`/tools/${id}`, {\n      method: 'DELETE',\n    });\n  }\n\n  async getLikedTools(params?: {\n    page?: number;\n    limit?: number;\n  }): Promise<ApiResponse<ToolsResponse>> {\n    const searchParams = new URLSearchParams();\n    if (params) {\n      Object.entries(params).forEach(([key, value]) => {\n        if (value !== undefined) {\n          searchParams.append(key, value.toString());\n        }\n      });\n    }\n    const query = searchParams.toString();\n    return this.request<ToolsResponse>(`/user/liked-tools${query ? `?${query}` : ''}`);\n  }\n\n  // 管理员API\n  async getAdminTools(params?: {\n    page?: number;\n    limit?: number;\n    status?: string;\n    search?: string;\n    sort?: string;\n    order?: string;\n  }): Promise<ApiResponse<ToolsResponse>> {\n    const searchParams = new URLSearchParams();\n    if (params) {\n      Object.entries(params).forEach(([key, value]) => {\n        if (value !== undefined) {\n          searchParams.append(key, value.toString());\n        }\n      });\n    }\n    const query = searchParams.toString();\n    return this.request<ToolsResponse>(`/admin/tools${query ? `?${query}` : ''}`);\n  }\n\n  async approveTool(id: string, data: {\n    reviewedBy?: string;\n    reviewNotes?: string;\n    launchDate?: string;\n  }): Promise<ApiResponse<Tool>> {\n    return this.request<Tool>(`/admin/tools/${id}/approve`, {\n      method: 'POST',\n      body: JSON.stringify(data),\n    });\n  }\n\n  async rejectTool(id: string, data: {\n    reviewedBy?: string;\n    rejectReason: string;\n  }): Promise<ApiResponse<Tool>> {\n    return this.request<Tool>(`/admin/tools/${id}/reject`, {\n      method: 'POST',\n      body: JSON.stringify(data),\n    });\n  }\n\n  async getAdminStats(timeRange?: string): Promise<ApiResponse<AdminStats>> {\n    const query = timeRange ? `?timeRange=${timeRange}` : '';\n    return this.request<AdminStats>(`/admin/stats${query}`);\n  }\n\n  // 分类API\n  async getCategories(): Promise<ApiResponse<CategoriesResponse>> {\n    return this.request<CategoriesResponse>('/categories');\n  }\n}\n\n// 创建默认的API客户端实例\nexport const apiClient = new ApiClient();\n\n// 导出API客户端类以便在需要时创建新实例\nexport { ApiClient };\n"], "names": [], "mappings": "AAAA,YAAY;;;;;AACZ;;AAEA,MAAM,eAAe,CAAA,GAAA,oHAAA,CAAA,gBAAa,AAAD;AAqIjC,MAAM;IACI,QAAgB;IAExB,YAAY,UAAkB,YAAY,CAAE;QAC1C,IAAI,CAAC,OAAO,GAAG;IACjB;IAEA,MAAc,QACZ,QAAgB,EAChB,UAAuB,CAAC,CAAC,EACA;QACzB,IAAI;YACF,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,GAAG,UAAU;YACxC,MAAM,SAAsB;gBAC1B,SAAS;oBACP,gBAAgB;oBAChB,GAAG,QAAQ,OAAO;gBACpB;gBACA,GAAG,OAAO;YACZ;YAEA,MAAM,WAAW,MAAM,MAAM,KAAK;YAClC,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,KAAK,KAAK,IAAI,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;YACxE;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uBAAuB;YACrC,OAAO;gBACL,SAAS;gBACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAClD;QACF;IACF;IAEA,UAAU;IACV,MAAM,SAAS,MAUd,EAAuC;QACtC,MAAM,eAAe,IAAI;QACzB,IAAI,QAAQ;YACV,OAAO,OAAO,CAAC,QAAQ,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;gBAC1C,IAAI,UAAU,WAAW;oBACvB,aAAa,MAAM,CAAC,KAAK,MAAM,QAAQ;gBACzC;YACF;QACF;QACA,MAAM,QAAQ,aAAa,QAAQ;QACnC,OAAO,IAAI,CAAC,OAAO,CAAgB,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC,EAAE,OAAO,GAAG,IAAI;IACxE;IAEA,MAAM,QAAQ,EAAU,EAA8B;QACpD,OAAO,IAAI,CAAC,OAAO,CAAO,CAAC,OAAO,EAAE,IAAI;IAC1C;IAEA,MAAM,WAAW,QAAuB,EAA8B;QACpE,OAAO,IAAI,CAAC,OAAO,CAAO,UAAU;YAClC,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,MAAM,WAAW,EAAU,EAAE,QAAuB,EAA8B;QAChF,OAAO,IAAI,CAAC,OAAO,CAAO,CAAC,OAAO,EAAE,IAAI,EAAE;YACxC,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,MAAM,WAAW,EAAU,EAA8B;QACvD,OAAO,IAAI,CAAC,OAAO,CAAO,CAAC,OAAO,EAAE,IAAI,EAAE;YACxC,QAAQ;QACV;IACF;IAEA,MAAM,cAAc,MAGnB,EAAuC;QACtC,MAAM,eAAe,IAAI;QACzB,IAAI,QAAQ;YACV,OAAO,OAAO,CAAC,QAAQ,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;gBAC1C,IAAI,UAAU,WAAW;oBACvB,aAAa,MAAM,CAAC,KAAK,MAAM,QAAQ;gBACzC;YACF;QACF;QACA,MAAM,QAAQ,aAAa,QAAQ;QACnC,OAAO,IAAI,CAAC,OAAO,CAAgB,CAAC,iBAAiB,EAAE,QAAQ,CAAC,CAAC,EAAE,OAAO,GAAG,IAAI;IACnF;IAEA,SAAS;IACT,MAAM,cAAc,MAOnB,EAAuC;QACtC,MAAM,eAAe,IAAI;QACzB,IAAI,QAAQ;YACV,OAAO,OAAO,CAAC,QAAQ,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;gBAC1C,IAAI,UAAU,WAAW;oBACvB,aAAa,MAAM,CAAC,KAAK,MAAM,QAAQ;gBACzC;YACF;QACF;QACA,MAAM,QAAQ,aAAa,QAAQ;QACnC,OAAO,IAAI,CAAC,OAAO,CAAgB,CAAC,YAAY,EAAE,QAAQ,CAAC,CAAC,EAAE,OAAO,GAAG,IAAI;IAC9E;IAEA,MAAM,YAAY,EAAU,EAAE,IAI7B,EAA8B;QAC7B,OAAO,IAAI,CAAC,OAAO,CAAO,CAAC,aAAa,EAAE,GAAG,QAAQ,CAAC,EAAE;YACtD,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,MAAM,WAAW,EAAU,EAAE,IAG5B,EAA8B;QAC7B,OAAO,IAAI,CAAC,OAAO,CAAO,CAAC,aAAa,EAAE,GAAG,OAAO,CAAC,EAAE;YACrD,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,MAAM,cAAc,SAAkB,EAAoC;QACxE,MAAM,QAAQ,YAAY,CAAC,WAAW,EAAE,WAAW,GAAG;QACtD,OAAO,IAAI,CAAC,OAAO,CAAa,CAAC,YAAY,EAAE,OAAO;IACxD;IAEA,QAAQ;IACR,MAAM,gBAA0D;QAC9D,OAAO,IAAI,CAAC,OAAO,CAAqB;IAC1C;AACF;AAGO,MAAM,YAAY,IAAI", "debugId": null}}, {"offset": {"line": 424, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/app/%5Blocale%5D/admin/tools/%5Bid%5D/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { useParams, useRouter, usePathname } from 'next/navigation';\nimport { useTranslations } from 'next-intl';\nimport Layout from '@/components/Layout';\nimport LoadingSpinner from '@/components/LoadingSpinner';\nimport ErrorMessage from '@/components/ErrorMessage';\nimport SuccessMessage from '@/components/SuccessMessage';\nimport { apiClient, Tool } from '@/lib/api';\nimport { Locale } from '@/i18n/config';\nimport {\n  ArrowLeft,\n  ExternalLink,\n  Calendar,\n  User,\n  Tag,\n  CheckCircle,\n  XCircle,\n  Clock,\n  AlertTriangle,\n  Globe,\n  DollarSign\n} from 'lucide-react';\n\n\n\nexport default function AdminToolDetailPage() {\n  const params = useParams();\n  const router = useRouter();\n  const pathname = usePathname();\n  const toolId = params?.id as string;\n\n  const t = useTranslations('admin');\n  const tCategories = useTranslations('categories');\n  const currentLocale = (pathname?.startsWith('/en') ? 'en' : 'zh') as Locale;\n\n  const [tool, setTool] = useState<Tool | null>(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const [successMessage, setSuccessMessage] = useState('');\n  const [showRejectModal, setShowRejectModal] = useState(false);\n  const [rejectReason, setRejectReason] = useState('');\n  const [isProcessing, setIsProcessing] = useState(false);\n\n  useEffect(() => {\n    if (params?.id) {\n      fetchTool(params.id as string);\n    }\n  }, [params?.id]);\n\n  const fetchTool = async (id: string) => {\n    try {\n      setLoading(true);\n      setError('');\n\n      const response = await apiClient.getTool(id);\n\n      if (response.success && response.data) {\n        setTool(response.data);\n      } else {\n        setError(response.error || t('errors.fetch_failed'));\n      }\n    } catch (err) {\n      setError(t('errors.network_error'));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleApprove = async () => {\n    if (!tool) return;\n\n    setIsProcessing(true);\n    try {\n      setError('');\n\n      const response = await apiClient.approveTool(tool._id, {\n        reviewedBy: 'admin', // In production, this should be the current logged-in admin\n        reviewNotes: t('success.tool_approved'),\n        launchDate: new Date().toISOString()\n      });\n\n      if (response.success) {\n        setSuccessMessage(t('success.tool_approved'));\n        // Refetch tool data to update status\n        await fetchTool(tool._id);\n      } else {\n        setError(response.error || t('errors.approve_failed'));\n      }\n    } catch (err) {\n      setError(t('errors.network_error'));\n    } finally {\n      setIsProcessing(false);\n    }\n  };\n\n  const handleReject = async () => {\n    if (!tool) return;\n\n    if (!rejectReason.trim()) {\n      setError(t('errors.reject_reason_required'));\n      return;\n    }\n\n    setIsProcessing(true);\n    try {\n      setError('');\n\n      const response = await apiClient.rejectTool(tool._id, {\n        reviewedBy: 'admin', // In production, this should be the current logged-in admin\n        rejectReason: rejectReason\n      });\n\n      if (response.success) {\n        setSuccessMessage(t('success.tool_rejected'));\n        setShowRejectModal(false);\n        setRejectReason('');\n        // Refetch tool data to update status\n        await fetchTool(tool._id);\n      } else {\n        setError(response.error || t('errors.reject_failed'));\n      }\n    } catch (err) {\n      setError(t('errors.network_error'));\n    } finally {\n      setIsProcessing(false);\n    }\n  };\n\n  const formatDate = (dateString: string) => {\n    return new Date(dateString).toLocaleDateString(locale === 'zh' ? 'zh-CN' : 'en-US', {\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric',\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  };\n\n  const getStatusBadge = (tool: { status: string; launchDate?: string }) => {\n    // Check if published: approved status and launchDate has passed\n    const isPublished = tool.status === 'approved' && tool.launchDate && new Date(tool.launchDate) <= new Date();\n\n    if (isPublished) {\n      return (\n        <span className=\"inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800\">\n          <CheckCircle className=\"w-4 h-4 mr-2\" />\n          {t('status_labels.published')}\n        </span>\n      );\n    }\n\n    switch (tool.status) {\n      case 'pending':\n        return (\n          <span className=\"inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-yellow-100 text-yellow-800\">\n            <Clock className=\"w-4 h-4 mr-2\" />\n            {t('status_labels.pending')}\n          </span>\n        );\n      case 'approved':\n        return (\n          <span className=\"inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800\">\n            <CheckCircle className=\"w-4 h-4 mr-2\" />\n            {t('status_labels.approved')}\n          </span>\n        );\n\n      case 'rejected':\n        return (\n          <span className=\"inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-red-100 text-red-800\">\n            <XCircle className=\"w-4 h-4 mr-2\" />\n            {t('status_labels.rejected')}\n          </span>\n        );\n      default:\n        return null;\n    }\n  };\n\n  if (loading) {\n    return (\n      <Layout>\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n          <LoadingSpinner size=\"lg\" className=\"py-20\" />\n        </div>\n      </Layout>\n    );\n  }\n\n  if (error || !tool) {\n    return (\n      <Layout>\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n          <div className=\"text-center py-12\">\n            <AlertTriangle className=\"mx-auto h-12 w-12 text-gray-400\" />\n            <h3 className=\"mt-2 text-sm font-medium text-gray-900\">\n              {error || t('errors.tool_not_found')}\n            </h3>\n            <div className=\"mt-6\">\n              <button\n                onClick={() => router.push(`/${currentLocale}/admin`)}\n                className=\"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700\"\n              >\n                <ArrowLeft className=\"w-4 h-4 mr-2\" />\n                {t('actions.back_to_admin')}\n              </button>\n            </div>\n          </div>\n        </div>\n      </Layout>\n    );\n  }\n\n  return (\n    <Layout>\n      <div className=\"max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        {/* Success/Error Messages */}\n        {successMessage && (\n          <SuccessMessage\n            message={successMessage}\n            onClose={() => setSuccessMessage('')}\n            className=\"mb-6\"\n          />\n        )}\n\n        {error && (\n          <ErrorMessage\n            message={error}\n            onClose={() => setError('')}\n            className=\"mb-6\"\n          />\n        )}\n\n        {/* Header */}\n        <div className=\"mb-8\">\n          <button\n            onClick={() => router.back()}\n            className=\"flex items-center text-gray-600 hover:text-gray-900 mb-4 transition-colors\"\n          >\n            <ArrowLeft className=\"w-4 h-4 mr-2\" />\n            {t('actions.back_to_review')}\n          </button>\n          \n          <div className=\"flex items-start justify-between\">\n            <div className=\"flex items-start space-x-6\">\n              <img\n                src={tool.logo}\n                alt={tool.name}\n                className=\"w-24 h-24 rounded-xl object-cover border border-gray-200 shadow-sm\"\n              />\n              <div>\n                <div className=\"flex items-center space-x-3 mb-2\">\n                  <h1 className=\"text-3xl font-bold text-gray-900\">{tool.name}</h1>\n                  {getStatusBadge(tool)}\n                </div>\n                <div className=\"flex items-center space-x-4 text-sm text-gray-600 mb-4\">\n                  <span className=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800\">\n                    {tCategories(`category_names.${tool.category}`) || tool.category}\n                  </span>\n                  <span className=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800\">\n                    <DollarSign className=\"w-3 h-3 mr-1\" />\n                    {t(`pricing_labels.${tool.pricing}`) || tool.pricing}\n                  </span>\n                  <a\n                    href={tool.website}\n                    target=\"_blank\"\n                    rel=\"noopener noreferrer\"\n                    className=\"inline-flex items-center text-blue-600 hover:text-blue-800 transition-colors\"\n                  >\n                    <Globe className=\"w-4 h-4 mr-1\" />\n                    {t('actions.visit_website')}\n                    <ExternalLink className=\"w-3 h-3 ml-1\" />\n                  </a>\n                </div>\n                <p className=\"text-gray-600 max-w-3xl\">{tool.tagline}</p>\n              </div>\n            </div>\n\n            {/* Action Buttons */}\n            {tool.status === 'pending' && (\n              <div className=\"flex space-x-3\">\n                <button\n                  onClick={handleApprove}\n                  disabled={isProcessing}\n                  className=\"px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors flex items-center\"\n                >\n                  <CheckCircle className=\"w-4 h-4 mr-2\" />\n                  {isProcessing ? t('actions.processing') : t('actions.approve')}\n                </button>\n                <button\n                  onClick={() => setShowRejectModal(true)}\n                  disabled={isProcessing}\n                  className=\"px-6 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors flex items-center\"\n                >\n                  <XCircle className=\"w-4 h-4 mr-2\" />\n                  {t('actions.reject')}\n                </button>\n              </div>\n            )}\n          </div>\n        </div>\n\n        <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-8\">\n          {/* Main Content */}\n          <div className=\"lg:col-span-2 space-y-8\">\n            {/* Description */}\n            <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n              <h2 className=\"text-xl font-semibold text-gray-900 mb-4\">{t('sections.tool_description')}</h2>\n              <div className=\"prose max-w-none\">\n                <p className=\"text-gray-700 leading-relaxed\">\n                  {tool.description}\n                </p>\n                {tool.longDescription && (\n                  <div className=\"mt-4\">\n                    <h3 className=\"text-lg font-medium text-gray-900 mb-2\">{t('sections.detailed_description')}</h3>\n                    <p className=\"text-gray-700 leading-relaxed\">\n                      {tool.longDescription}\n                    </p>\n                  </div>\n                )}\n              </div>\n            </div>\n\n            {/* Tags */}\n            <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n              <h2 className=\"text-xl font-semibold text-gray-900 mb-4\">{t('sections.tags')}</h2>\n              <div className=\"flex flex-wrap gap-2\">\n                {tool.tags.map((tag, index) => (\n                  <span\n                    key={index}\n                    className=\"inline-flex items-center px-3 py-1 rounded-md text-sm font-medium bg-blue-100 text-blue-800\"\n                  >\n                    <Tag className=\"w-3 h-3 mr-1\" />\n                    {tag}\n                  </span>\n                ))}\n              </div>\n            </div>\n\n            {/* Screenshots */}\n            {tool.screenshots && tool.screenshots.length > 0 && (\n              <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n                <h2 className=\"text-xl font-semibold text-gray-900 mb-4\">{t('sections.screenshot_preview')}</h2>\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                  {tool.screenshots.map((screenshot, index) => (\n                    <img\n                      key={index}\n                      src={screenshot}\n                      alt={`${tool.name} ${t('sections.screenshot_preview')} ${index + 1}`}\n                      className=\"w-full h-48 object-cover rounded-lg border border-gray-200\"\n                    />\n                  ))}\n                </div>\n              </div>\n            )}\n          </div>\n\n          {/* Sidebar */}\n          <div className=\"space-y-6\">\n            {/* Submission Info */}\n            <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n              <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">{t('sections.submission_info')}</h3>\n              <div className=\"space-y-4\">\n                <div className=\"flex items-center\">\n                  <User className=\"w-5 h-5 text-gray-400 mr-3\" />\n                  <div>\n                    <div className=\"text-sm font-medium text-gray-900\">{tool.submittedBy}</div>\n                    <div className=\"text-sm text-gray-500\">{t('fields.submitter_id')}</div>\n                  </div>\n                </div>\n                \n                <div className=\"flex items-center\">\n                  <Calendar className=\"w-5 h-5 text-gray-400 mr-3\" />\n                  <div>\n                    <div className=\"text-sm font-medium text-gray-900\">{formatDate(tool.submittedAt)}</div>\n                    <div className=\"text-sm text-gray-500\">{t('fields.submission_time')}</div>\n                  </div>\n                </div>\n                \n                {tool.selectedLaunchDate && (\n                  <div className=\"flex items-center\">\n                    <Clock className=\"w-5 h-5 text-gray-400 mr-3\" />\n                    <div>\n                      <div className=\"text-sm font-medium text-gray-900\">\n                        {new Date(tool.selectedLaunchDate).toLocaleDateString(locale === 'zh' ? 'zh-CN' : 'en-US')}\n                      </div>\n                      <div className=\"text-sm text-gray-500\">{t('fields.selected_launch_date')}</div>\n                    </div>\n                  </div>\n                )}\n\n                {tool.launchDate && (\n                  <div className=\"flex items-center\">\n                    <CheckCircle className=\"w-5 h-5 text-gray-400 mr-3\" />\n                    <div>\n                      <div className=\"text-sm font-medium text-gray-900\">\n                        {new Date(tool.launchDate).toLocaleDateString(locale === 'zh' ? 'zh-CN' : 'en-US')}\n                      </div>\n                      <div className=\"text-sm text-gray-500\">{t('fields.actual_launch_date')}</div>\n                    </div>\n                  </div>\n                )}\n              </div>\n            </div>\n\n            {/* Review Guidelines */}\n            <div className=\"bg-blue-50 border border-blue-200 rounded-lg p-6\">\n              <div className=\"flex items-start\">\n                <AlertTriangle className=\"w-5 h-5 text-blue-600 mr-3 mt-0.5 flex-shrink-0\" />\n                <div>\n                  <h3 className=\"text-sm font-medium text-blue-900 mb-2\">{t('sections.review_guidelines')}</h3>\n                  <ul className=\"text-sm text-blue-800 space-y-1\">\n                    <li>• {t('guidelines.verify_website')}</li>\n                    <li>• {t('guidelines.check_description')}</li>\n                    <li>• {t('guidelines.confirm_category')}</li>\n                    <li>• {t('guidelines.evaluate_quality')}</li>\n                    <li>• {t('guidelines.check_duplicates')}</li>\n                  </ul>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Reject Modal */}\n        {showRejectModal && (\n          <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50\">\n            <div className=\"bg-white rounded-lg max-w-md w-full p-6\">\n              <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">{t('reject_modal.title')}</h3>\n              <p className=\"text-sm text-gray-600 mb-4\">\n                {t('reject_modal.description')}\n              </p>\n              <textarea\n                value={rejectReason}\n                onChange={(e) => setRejectReason(e.target.value)}\n                rows={4}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                placeholder={t('reject_modal.placeholder')}\n              />\n              <div className=\"flex justify-end space-x-3 mt-6\">\n                <button\n                  onClick={() => {\n                    setShowRejectModal(false);\n                    setRejectReason('');\n                  }}\n                  disabled={isProcessing}\n                  className=\"px-4 py-2 text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors\"\n                >\n                  {t('actions.cancel')}\n                </button>\n                <button\n                  onClick={handleReject}\n                  disabled={!rejectReason.trim() || isProcessing}\n                  className=\"px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors\"\n                >\n                  {isProcessing ? t('actions.processing') : t('actions.confirm_reject')}\n                </button>\n              </div>\n            </div>\n          </div>\n        )}\n      </div>\n    </Layout>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAXA;;;;;;;;;;AA2Be,SAAS;;IACtB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,SAAS,QAAQ;IAEvB,MAAM,IAAI,CAAA,GAAA,yMAAA,CAAA,kBAAe,AAAD,EAAE;IAC1B,MAAM,cAAc,CAAA,GAAA,yMAAA,CAAA,kBAAe,AAAD,EAAE;IACpC,MAAM,gBAAiB,UAAU,WAAW,SAAS,OAAO;IAE5D,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;yCAAE;YACR,IAAI,QAAQ,IAAI;gBACd,UAAU,OAAO,EAAE;YACrB;QACF;wCAAG;QAAC,QAAQ;KAAG;IAEf,MAAM,YAAY,OAAO;QACvB,IAAI;YACF,WAAW;YACX,SAAS;YAET,MAAM,WAAW,MAAM,oHAAA,CAAA,YAAS,CAAC,OAAO,CAAC;YAEzC,IAAI,SAAS,OAAO,IAAI,SAAS,IAAI,EAAE;gBACrC,QAAQ,SAAS,IAAI;YACvB,OAAO;gBACL,SAAS,SAAS,KAAK,IAAI,EAAE;YAC/B;QACF,EAAE,OAAO,KAAK;YACZ,SAAS,EAAE;QACb,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,gBAAgB;QACpB,IAAI,CAAC,MAAM;QAEX,gBAAgB;QAChB,IAAI;YACF,SAAS;YAET,MAAM,WAAW,MAAM,oHAAA,CAAA,YAAS,CAAC,WAAW,CAAC,KAAK,GAAG,EAAE;gBACrD,YAAY;gBACZ,aAAa,EAAE;gBACf,YAAY,IAAI,OAAO,WAAW;YACpC;YAEA,IAAI,SAAS,OAAO,EAAE;gBACpB,kBAAkB,EAAE;gBACpB,qCAAqC;gBACrC,MAAM,UAAU,KAAK,GAAG;YAC1B,OAAO;gBACL,SAAS,SAAS,KAAK,IAAI,EAAE;YAC/B;QACF,EAAE,OAAO,KAAK;YACZ,SAAS,EAAE;QACb,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,MAAM,eAAe;QACnB,IAAI,CAAC,MAAM;QAEX,IAAI,CAAC,aAAa,IAAI,IAAI;YACxB,SAAS,EAAE;YACX;QACF;QAEA,gBAAgB;QAChB,IAAI;YACF,SAAS;YAET,MAAM,WAAW,MAAM,oHAAA,CAAA,YAAS,CAAC,UAAU,CAAC,KAAK,GAAG,EAAE;gBACpD,YAAY;gBACZ,cAAc;YAChB;YAEA,IAAI,SAAS,OAAO,EAAE;gBACpB,kBAAkB,EAAE;gBACpB,mBAAmB;gBACnB,gBAAgB;gBAChB,qCAAqC;gBACrC,MAAM,UAAU,KAAK,GAAG;YAC1B,OAAO;gBACL,SAAS,SAAS,KAAK,IAAI,EAAE;YAC/B;QACF,EAAE,OAAO,KAAK;YACZ,SAAS,EAAE;QACb,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,OAAO,IAAI,KAAK,YAAY,kBAAkB,CAAC,WAAW,OAAO,UAAU,SAAS;YAClF,MAAM;YACN,OAAO;YACP,KAAK;YACL,MAAM;YACN,QAAQ;QACV;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,gEAAgE;QAChE,MAAM,cAAc,KAAK,MAAM,KAAK,cAAc,KAAK,UAAU,IAAI,IAAI,KAAK,KAAK,UAAU,KAAK,IAAI;QAEtG,IAAI,aAAa;YACf,qBACE,6LAAC;gBAAK,WAAU;;kCACd,6LAAC,8NAAA,CAAA,cAAW;wBAAC,WAAU;;;;;;oBACtB,EAAE;;;;;;;QAGT;QAEA,OAAQ,KAAK,MAAM;YACjB,KAAK;gBACH,qBACE,6LAAC;oBAAK,WAAU;;sCACd,6LAAC,uMAAA,CAAA,QAAK;4BAAC,WAAU;;;;;;wBAChB,EAAE;;;;;;;YAGT,KAAK;gBACH,qBACE,6LAAC;oBAAK,WAAU;;sCACd,6LAAC,8NAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;wBACtB,EAAE;;;;;;;YAIT,KAAK;gBACH,qBACE,6LAAC;oBAAK,WAAU;;sCACd,6LAAC,+MAAA,CAAA,UAAO;4BAAC,WAAU;;;;;;wBAClB,EAAE;;;;;;;YAGT;gBACE,OAAO;QACX;IACF;IAEA,IAAI,SAAS;QACX,qBACE,6LAAC,+HAAA,CAAA,UAAM;sBACL,cAAA,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,uIAAA,CAAA,UAAc;oBAAC,MAAK;oBAAK,WAAU;;;;;;;;;;;;;;;;IAI5C;IAEA,IAAI,SAAS,CAAC,MAAM;QAClB,qBACE,6LAAC,+HAAA,CAAA,UAAM;sBACL,cAAA,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,2NAAA,CAAA,gBAAa;4BAAC,WAAU;;;;;;sCACzB,6LAAC;4BAAG,WAAU;sCACX,SAAS,EAAE;;;;;;sCAEd,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCACC,SAAS,IAAM,OAAO,IAAI,CAAC,CAAC,CAAC,EAAE,cAAc,MAAM,CAAC;gCACpD,WAAU;;kDAEV,6LAAC,mNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;oCACpB,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAOjB;IAEA,qBACE,6LAAC,+HAAA,CAAA,UAAM;kBACL,cAAA,6LAAC;YAAI,WAAU;;gBAEZ,gCACC,6LAAC,uIAAA,CAAA,UAAc;oBACb,SAAS;oBACT,SAAS,IAAM,kBAAkB;oBACjC,WAAU;;;;;;gBAIb,uBACC,6LAAC,qIAAA,CAAA,UAAY;oBACX,SAAS;oBACT,SAAS,IAAM,SAAS;oBACxB,WAAU;;;;;;8BAKd,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BACC,SAAS,IAAM,OAAO,IAAI;4BAC1B,WAAU;;8CAEV,6LAAC,mNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;gCACpB,EAAE;;;;;;;sCAGL,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,KAAK,KAAK,IAAI;4CACd,KAAK,KAAK,IAAI;4CACd,WAAU;;;;;;sDAEZ,6LAAC;;8DACC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAG,WAAU;sEAAoC,KAAK,IAAI;;;;;;wDAC1D,eAAe;;;;;;;8DAElB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;sEACb,YAAY,CAAC,eAAe,EAAE,KAAK,QAAQ,EAAE,KAAK,KAAK,QAAQ;;;;;;sEAElE,6LAAC;4DAAK,WAAU;;8EACd,6LAAC,qNAAA,CAAA,aAAU;oEAAC,WAAU;;;;;;gEACrB,EAAE,CAAC,eAAe,EAAE,KAAK,OAAO,EAAE,KAAK,KAAK,OAAO;;;;;;;sEAEtD,6LAAC;4DACC,MAAM,KAAK,OAAO;4DAClB,QAAO;4DACP,KAAI;4DACJ,WAAU;;8EAEV,6LAAC,uMAAA,CAAA,QAAK;oEAAC,WAAU;;;;;;gEAChB,EAAE;8EACH,6LAAC,yNAAA,CAAA,eAAY;oEAAC,WAAU;;;;;;;;;;;;;;;;;;8DAG5B,6LAAC;oDAAE,WAAU;8DAA2B,KAAK,OAAO;;;;;;;;;;;;;;;;;;gCAKvD,KAAK,MAAM,KAAK,2BACf,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,SAAS;4CACT,UAAU;4CACV,WAAU;;8DAEV,6LAAC,8NAAA,CAAA,cAAW;oDAAC,WAAU;;;;;;gDACtB,eAAe,EAAE,wBAAwB,EAAE;;;;;;;sDAE9C,6LAAC;4CACC,SAAS,IAAM,mBAAmB;4CAClC,UAAU;4CACV,WAAU;;8DAEV,6LAAC,+MAAA,CAAA,UAAO;oDAAC,WAAU;;;;;;gDAClB,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;8BAOb,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAA4C,EAAE;;;;;;sDAC5D,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAE,WAAU;8DACV,KAAK,WAAW;;;;;;gDAElB,KAAK,eAAe,kBACnB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAG,WAAU;sEAA0C,EAAE;;;;;;sEAC1D,6LAAC;4DAAE,WAAU;sEACV,KAAK,eAAe;;;;;;;;;;;;;;;;;;;;;;;;8CAQ/B,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAA4C,EAAE;;;;;;sDAC5D,6LAAC;4CAAI,WAAU;sDACZ,KAAK,IAAI,CAAC,GAAG,CAAC,CAAC,KAAK,sBACnB,6LAAC;oDAEC,WAAU;;sEAEV,6LAAC,mMAAA,CAAA,MAAG;4DAAC,WAAU;;;;;;wDACd;;mDAJI;;;;;;;;;;;;;;;;gCAWZ,KAAK,WAAW,IAAI,KAAK,WAAW,CAAC,MAAM,GAAG,mBAC7C,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAA4C,EAAE;;;;;;sDAC5D,6LAAC;4CAAI,WAAU;sDACZ,KAAK,WAAW,CAAC,GAAG,CAAC,CAAC,YAAY,sBACjC,6LAAC;oDAEC,KAAK;oDACL,KAAK,GAAG,KAAK,IAAI,CAAC,CAAC,EAAE,EAAE,+BAA+B,CAAC,EAAE,QAAQ,GAAG;oDACpE,WAAU;mDAHL;;;;;;;;;;;;;;;;;;;;;;sCAYjB,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAA4C,EAAE;;;;;;sDAC5D,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,qMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;sEAChB,6LAAC;;8EACC,6LAAC;oEAAI,WAAU;8EAAqC,KAAK,WAAW;;;;;;8EACpE,6LAAC;oEAAI,WAAU;8EAAyB,EAAE;;;;;;;;;;;;;;;;;;8DAI9C,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,6MAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;sEACpB,6LAAC;;8EACC,6LAAC;oEAAI,WAAU;8EAAqC,WAAW,KAAK,WAAW;;;;;;8EAC/E,6LAAC;oEAAI,WAAU;8EAAyB,EAAE;;;;;;;;;;;;;;;;;;gDAI7C,KAAK,kBAAkB,kBACtB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,uMAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;sEACjB,6LAAC;;8EACC,6LAAC;oEAAI,WAAU;8EACZ,IAAI,KAAK,KAAK,kBAAkB,EAAE,kBAAkB,CAAC,WAAW,OAAO,UAAU;;;;;;8EAEpF,6LAAC;oEAAI,WAAU;8EAAyB,EAAE;;;;;;;;;;;;;;;;;;gDAK/C,KAAK,UAAU,kBACd,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,8NAAA,CAAA,cAAW;4DAAC,WAAU;;;;;;sEACvB,6LAAC;;8EACC,6LAAC;oEAAI,WAAU;8EACZ,IAAI,KAAK,KAAK,UAAU,EAAE,kBAAkB,CAAC,WAAW,OAAO,UAAU;;;;;;8EAE5E,6LAAC;oEAAI,WAAU;8EAAyB,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAQpD,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,2NAAA,CAAA,gBAAa;gDAAC,WAAU;;;;;;0DACzB,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAA0C,EAAE;;;;;;kEAC1D,6LAAC;wDAAG,WAAU;;0EACZ,6LAAC;;oEAAG;oEAAG,EAAE;;;;;;;0EACT,6LAAC;;oEAAG;oEAAG,EAAE;;;;;;;0EACT,6LAAC;;oEAAG;oEAAG,EAAE;;;;;;;0EACT,6LAAC;;oEAAG;oEAAG,EAAE;;;;;;;0EACT,6LAAC;;oEAAG;oEAAG,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBASpB,iCACC,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAA4C,EAAE;;;;;;0CAC5D,6LAAC;gCAAE,WAAU;0CACV,EAAE;;;;;;0CAEL,6LAAC;gCACC,OAAO;gCACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;gCAC/C,MAAM;gCACN,WAAU;gCACV,aAAa,EAAE;;;;;;0CAEjB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,SAAS;4CACP,mBAAmB;4CACnB,gBAAgB;wCAClB;wCACA,UAAU;wCACV,WAAU;kDAET,EAAE;;;;;;kDAEL,6LAAC;wCACC,SAAS;wCACT,UAAU,CAAC,aAAa,IAAI,MAAM;wCAClC,WAAU;kDAET,eAAe,EAAE,wBAAwB,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS9D;GAvbwB;;QACP,qIAAA,CAAA,YAAS;QACT,qIAAA,CAAA,YAAS;QACP,qIAAA,CAAA,cAAW;QAGlB,yMAAA,CAAA,kBAAe;QACL,yMAAA,CAAA,kBAAe;;;KAPb", "debugId": null}}]}