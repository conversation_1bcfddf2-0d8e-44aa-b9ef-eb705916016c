import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import dbConnect from '@/lib/mongodb';
import Tool from '@/models/Tool';
import User from '@/models/User';
import { authOptions } from '@/lib/auth';
import mongoose from 'mongoose';
import { getApiMessage, getLocaleFromRequest } from '@/lib/api-messages';

// GET /api/tools/[id] - 获取单个工具详情
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    await dbConnect();

    const { id } = await params;
    const locale = getLocaleFromRequest(request);

    // 验证ID格式
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return NextResponse.json(
        { success: false, message: getApiMessage(locale, 'errors.invalid_request') },
        { status: 400 }
      );
    }

    // 查找工具
    const tool = await Tool.findById(id);

    if (!tool) {
      return NextResponse.json(
        { success: false, message: getApiMessage(locale, 'tools.not_found') },
        { status: 404 }
      );
    }

    // 检查是否需要认证（对于草稿状态的工具）
    if (tool.status === 'draft') {
      const session = await getServerSession(authOptions);
      if (!session?.user?.email) {
        return NextResponse.json(
          { success: false, message: getApiMessage(locale, 'user.unauthorized') },
          { status: 401 }
        );
      }

      const user = await User.findOne({ email: session.user.email });
      if (!user || tool.submittedBy !== user._id.toString()) {
        return NextResponse.json(
          { success: false, message: getApiMessage(locale, 'errors.forbidden') },
          { status: 403 }
        );
      }
    }

    // 如果是公开访问已发布的工具，增加浏览量
    const isPublished = tool.status === 'approved' && tool.launchDate && new Date(tool.launchDate) <= new Date();
    if (isPublished && tool.isActive) {
      await Tool.findByIdAndUpdate(id, { $inc: { views: 1 } });
    }

    // 根据访问权限返回不同的数据
    let responseData;
    if (tool.status === 'draft') {
      // 草稿状态返回完整信息给所有者
      responseData = tool;
    } else {
      // 公开工具隐藏敏感信息
      responseData = {
        ...tool.toObject(),
        submittedBy: undefined,
        reviewNotes: undefined,
        reviewedBy: undefined
      };
    }

    return NextResponse.json({
      success: true,
      data: responseData
    });

  } catch (error) {
    console.error('Error fetching tool:', error);
    const locale = getLocaleFromRequest(request);
    return NextResponse.json(
      { success: false, message: getApiMessage(locale, 'tools.fetch_failed') },
      { status: 500 }
    );
  }
}

// PUT /api/tools/[id] - 更新工具信息
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // 检查用户认证
    const session = await getServerSession(authOptions);
    const locale = getLocaleFromRequest(request);

    if (!session?.user?.email) {
      return NextResponse.json(
        { success: false, error: getApiMessage(locale, 'user.unauthorized') },
        { status: 401 }
      );
    }

    await dbConnect();

    const { id } = await params;
    const body = await request.json();

    // 验证ID格式
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return NextResponse.json(
        { success: false, error: getApiMessage(locale, 'errors.invalid_request') },
        { status: 400 }
      );
    }

    // 获取用户信息
    const user = await User.findOne({ email: session.user.email });
    if (!user) {
      return NextResponse.json(
        { success: false, error: getApiMessage(locale, 'user.not_found') },
        { status: 404 }
      );
    }

    // 查找工具
    const tool = await Tool.findById(id);
    if (!tool) {
      return NextResponse.json(
        { success: false, error: getApiMessage(locale, 'tools.not_found') },
        { status: 404 }
      );
    }

    // 检查用户权限（只能编辑自己提交的工具）
    if (tool.submittedBy !== user._id.toString()) {
      return NextResponse.json(
        { success: false, error: getApiMessage(locale, 'errors.forbidden') },
        { status: 403 }
      );
    }

    // 检查工具状态和编辑权限
    const allowedStatuses = ['draft', 'pending', 'rejected', 'approved', 'published'];
    if (!allowedStatuses.includes(tool.status)) {
      return NextResponse.json(
        { success: false, error: getApiMessage(locale, 'tools.edit_not_allowed') },
        { status: 400 }
      );
    }

    // 根据工具状态确定允许更新的字段
    let allowedUpdates: string[] = [];

    if (['draft', 'pending', 'rejected'].includes(tool.status)) {
      // 草稿、待审核、被拒绝状态：可以编辑所有基本信息
      allowedUpdates = [
        'name', 'tagline', 'description', 'website', 'logo',
        'category', 'pricing', 'tags'
      ];
    } else if (tool.status === 'approved') {
      // 已通过审核：检查是否已发布
      const isPublished = tool.launchDate && new Date(tool.launchDate) <= new Date();
      if (isPublished) {
        // 已发布：只能编辑基础描述信息
        allowedUpdates = [
          'name', 'tagline', 'description'
        ];
      } else {
        // 未发布：可以编辑基础信息，但不能修改URL
        allowedUpdates = [
          'name', 'tagline', 'description', 'logo'
        ];
      }
    }

    const updates: any = {};
    for (const field of allowedUpdates) {
      if (body[field] !== undefined) {
        updates[field] = body[field];
      }
    }

    // 如果更新了名称，检查是否重复
    if (updates.name && updates.name !== tool.name) {
      const existingTool = await Tool.findOne({ 
        name: updates.name, 
        _id: { $ne: id } 
      });
      if (existingTool) {
        return NextResponse.json(
          { success: false, error: getApiMessage(locale, 'tools.name_exists') },
          { status: 400 }
        );
      }
    }

    // 如果是被拒绝的工具，编辑后需要重置状态
    if (tool.status === 'rejected') {
      updates.status = 'draft';
      updates.isActive = true;
      // 清除拒绝相关信息，但保持付费信息
      updates.reviewedAt = undefined;
      updates.reviewedBy = undefined;
      updates.reviewNotes = undefined;
    }

    // 准备更新操作
    const updateOperation: any = { $set: updates };

    // 如果是被拒绝的工具，需要删除一些字段
    if (tool.status === 'rejected') {
      updateOperation.$unset = {
        reviewedAt: 1,
        reviewedBy: 1,
        reviewNotes: 1
      };
    }

    // 执行更新
    const updatedTool = await Tool.findByIdAndUpdate(
      id,
      updateOperation,
      { new: true, runValidators: true }
    ).select('-submittedBy -reviewNotes -reviewedBy');

    return NextResponse.json({
      success: true,
      data: updatedTool,
      message: getApiMessage(locale, 'tools.update_success')
    });

  } catch (error) {
    console.error('Error updating tool:', error);
    const locale = getLocaleFromRequest(request);

    if ((error as any)?.name === 'ValidationError') {
      const validationErrors = Object.values((error as any).errors).map((err: any) => err.message);
      return NextResponse.json(
        { success: false, error: getApiMessage(locale, 'errors.validation_failed'), details: validationErrors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { success: false, error: getApiMessage(locale, 'tools.update_failed') },
      { status: 500 }
    );
  }
}

// DELETE /api/tools/[id] - 删除工具
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    await dbConnect();
    const locale = getLocaleFromRequest(request);

    const { id } = params;

    // 验证ID格式
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return NextResponse.json(
        { success: false, error: getApiMessage(locale, 'errors.invalid_request') },
        { status: 400 }
      );
    }

    // 查找并删除工具
    const tool = await Tool.findByIdAndDelete(id);
    if (!tool) {
      return NextResponse.json(
        { success: false, error: getApiMessage(locale, 'tools.not_found') },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      message: getApiMessage(locale, 'tools.delete_success')
    });

  } catch (error) {
    console.error('Error deleting tool:', error);
    const locale = getLocaleFromRequest(request);
    return NextResponse.json(
      { success: false, error: getApiMessage(locale, 'tools.delete_failed') },
      { status: 500 }
    );
  }
}
