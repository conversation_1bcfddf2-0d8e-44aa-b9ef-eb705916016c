import React, { Fragment } from 'react';
import { getServerSession } from 'next-auth/next';
import { redirect } from 'next/navigation';
import { authOptions } from '@/lib/auth';
import dbConnect from '@/lib/mongodb';
import User from '@/models/User';
import Tool from '@/models/Tool';
import { Tool as ToolType } from '@/lib/api';
import LikedToolsClient from '@/components/profile/LikedToolsClient';

// 服务端获取用户收藏的工具
async function getLikedTools(userEmail: string): Promise<ToolType[]> {
  await dbConnect();

  const user = await User.findOne({ email: userEmail });
  if (!user) {
    return [];
  }

  // 获取用户收藏的工具ID列表
  const likedToolIds = user.likedTools || [];

  if (likedToolIds.length === 0) {
    return [];
  }

  // 查询收藏的工具详情
  const tools = await Tool.find({
    _id: { $in: likedToolIds },
    isActive: true,
    status: 'approved'
  })
  .sort({ updatedAt: -1 })
  .lean();

  // 转换为前端需要的格式
  return tools.map(tool => ({
    _id: tool._id.toString(),
    name: tool.name,
    description: tool.description,
    website: tool.website,
    logo: tool.logo,
    category: tool.category,
    tags: tool.tags || [],
    pricing: tool.pricing,
    views: tool.views || 0,
    likes: tool.likes || 0,
    status: tool.status,
    isActive: tool.isActive,
    createdAt: tool.createdAt,
    updatedAt: tool.updatedAt,
    launchDate: tool.launchDate,
    submittedBy: tool.submittedBy?.toString(),
    reviewedBy: tool.reviewedBy?.toString(),
    reviewNotes: tool.reviewNotes,
    rejectReason: tool.rejectReason,
    likedBy: tool.likedBy?.map((id: any) => id.toString()) || [],
    tagline: tool.tagline
  }));
}

export default async function LikedToolsPage() {
  // 服务端获取session
  const session = await getServerSession(authOptions);

  // 如果未登录，重定向到首页
  if (!session?.user?.email) {
    redirect('/');
  }

  // 服务端获取收藏的工具
  const likedTools = await getLikedTools(session.user.email);

  return (
    <Fragment>
      <LikedToolsClient initialTools={likedTools} />
    </Fragment>
  );
}
