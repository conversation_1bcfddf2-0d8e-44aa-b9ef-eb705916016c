import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import dbConnect from '@/lib/mongodb';
import Tool from '@/models/Tool';
import User from '@/models/User';
import mongoose from 'mongoose';
import { getLocaleFromRequest, getApiMessage } from '@/lib/i18n-server';

// POST /api/tools/[id]/reapply - 重新申请被拒绝的工具
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // 检查用户认证
    const session = await getServerSession(authOptions);
    const locale = getLocaleFromRequest(request);

    if (!session?.user?.email) {
      return NextResponse.json(
        { success: false, message: getApiMessage(locale, 'user.unauthorized') },
        { status: 401 }
      );
    }

    await dbConnect();

    const { id } = await params;

    // 验证ID格式
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return NextResponse.json(
        { success: false, message: getApiMessage(locale, 'errors.invalid_request') },
        { status: 400 }
      );
    }

    // 获取用户信息
    const user = await User.findOne({ email: session.user.email });
    if (!user) {
      return NextResponse.json(
        { success: false, message: getApiMessage(locale, 'user.not_found') },
        { status: 404 }
      );
    }

    // 查找工具
    const tool = await Tool.findById(id);
    if (!tool) {
      return NextResponse.json(
        { success: false, message: getApiMessage(locale, 'tools.not_found') },
        { status: 404 }
      );
    }

    // 检查工具所有权
    if (tool.submittedBy !== user._id.toString()) {
      return NextResponse.json(
        { success: false, message: getApiMessage(locale, 'errors.forbidden') },
        { status: 403 }
      );
    }

    // 检查工具状态 - 只有被拒绝的工具可以重新申请
    if (tool.status !== 'rejected') {
      return NextResponse.json(
        { success: false, message: getApiMessage(locale, 'tools.reapply_not_allowed') },
        { status: 400 }
      );
    }

    // 重置工具状态为draft，保持付费相关信息
    // 使用$unset来删除字段
    const unsetFields: any = {
      reviewedAt: 1,
      reviewedBy: 1,
      reviewNotes: 1,
      selectedLaunchDate: 1,
      launchDate: 1
    };

    const updatedTool = await Tool.findByIdAndUpdate(
      id,
      {
        $set: {
          status: 'draft',
          isActive: true,
          launchDateSelected: false
        },
        $unset: unsetFields
      },
      { new: true, runValidators: true }
    );

    return NextResponse.json({
      success: true,
      data: updatedTool,
      message: getApiMessage(locale, 'tools.reapply_success')
    });

  } catch (error) {
    console.error('Tool reapply error:', error);
    const locale = getLocaleFromRequest(request);
    return NextResponse.json(
      { success: false, message: getApiMessage(locale, 'errors.internal_error') },
      { status: 500 }
    );
  }
}
