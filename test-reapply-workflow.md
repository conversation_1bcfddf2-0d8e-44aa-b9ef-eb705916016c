# 重新申请工作流程测试指南

## 测试步骤

### 1. 准备测试数据
需要一个状态为 'rejected' 的工具记录，可以通过以下方式创建：
- 提交一个工具
- 管理员拒绝该工具

### 2. 测试重新申请API
```bash
# 测试重新申请API端点
curl -X POST http://localhost:3003/api/tools/[TOOL_ID]/reapply \
  -H "Content-Type: application/json"
```

### 3. 测试UI工作流程

#### 3.1 访问已提交工具页面
- 登录用户账户
- 访问 `/profile/submitted`
- 查看被拒绝的工具

#### 3.2 测试编辑功能
- 点击 "编辑信息" 按钮
- 验证跳转到编辑页面
- 修改工具信息
- 保存更改
- 验证工具状态自动重置为 'draft'

#### 3.3 测试重新申请功能
- 在已提交工具页面，找到被拒绝的工具
- 点击 "重新申请" 按钮
- 验证跳转到重新申请launch date页面
- 验证页面显示之前的付费状态信息
- 选择发布日期和选项
- 提交申请

### 4. 验证结果

#### 4.1 数据库状态验证
- 工具状态应该从 'rejected' 变为 'draft'，然后变为 'pending'
- 拒绝相关字段应该被清除（reviewedAt, reviewedBy, reviewNotes）
- 付费状态应该保持不变

#### 4.2 UI状态验证
- 工具在已提交页面的状态应该更新
- 重新申请按钮应该消失（因为状态不再是rejected）
- 应该显示新的launch date信息

## 预期行为

### API层面
1. `/api/tools/[id]/reapply` 端点应该：
   - 验证工具状态为 'rejected'
   - 重置状态为 'draft'
   - 清除拒绝相关字段
   - 保持付费状态

2. `/api/tools/[id]` PUT端点应该：
   - 检测到rejected工具被编辑
   - 自动重置状态为 'draft'
   - 清除拒绝相关字段

### UI层面
1. SubmittedToolsList组件应该：
   - 为rejected工具显示编辑和重新申请按钮
   - 重新申请成功后跳转到重新申请页面

2. 重新申请launch date页面应该：
   - 显示工具信息
   - 显示之前的付费状态
   - 复用LaunchDateSelector组件
   - 保持付费状态逻辑

## 测试用例

### 用例1：免费用户重新申请
- 用户之前选择免费选项
- 重新申请时应该可以选择免费或升级到付费

### 用例2：付费用户重新申请
- 用户之前已付费
- 重新申请时应该保持付费状态，可以选择任意日期

### 用例3：编辑后自动重置
- 编辑被拒绝的工具信息
- 保存后状态应该自动变为draft
- 可以重新设置launch date

## 错误处理测试

1. 尝试重新申请非rejected状态的工具
2. 尝试重新申请不存在的工具
3. 网络错误情况下的处理
4. 权限验证（只能重新申请自己的工具）
